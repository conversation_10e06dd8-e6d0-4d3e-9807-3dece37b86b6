//
//  SwiftDataModels.swift
//  PrayerEase
//
//  Created by <PERSON><PERSON><PERSON> on 12/24/24.
//

import Foundation
import SwiftData
import CoreLocation
import Adhan

// MARK: - User Settings Model
@Model
final class UserSettings {
    var id: UUID
    var calculationMethodRawValue: String
    var madhabRawValue: Int
    var notificationSettings: [String: Bool]
    var beforeNotificationSettings: [String: Bool]
    var beforeMinutes: Int
    var createdAt: Date
    var updatedAt: Date
    
    // Computed properties for type-safe access
    var calculationMethod: CalculationMethod {
        get { CalculationMethod(rawValue: calculationMethodRawValue) ?? .turkey }
        set { calculationMethodRawValue = newValue.rawValue }
    }
    
    var madhab: Madhab {
        get { Madhab(rawValue: madhabRawValue) ?? .shafi }
        set { madhabRawValue = newValue.rawValue }
    }
    
    init(
        calculationMethod: CalculationMethod = .turkey,
        madhab: Madhab = .shafi,
        notificationSettings: [String: Bool] = AppConstants.App.defaultNotificationSettings,
        beforeNotificationSettings: [String: Bool] = AppConstants.App.defaultBeforeNotificationSettings,
        beforeMinutes: Int = AppConstants.Notifications.defaultBeforeMinutes
    ) {
        self.id = UUID()
        self.calculationMethodRawValue = calculationMethod.rawValue
        self.madhabRawValue = madhab.rawValue
        self.notificationSettings = notificationSettings
        self.beforeNotificationSettings = beforeNotificationSettings
        self.beforeMinutes = beforeMinutes
        self.createdAt = Date()
        self.updatedAt = Date()
    }
    
    func updateTimestamp() {
        self.updatedAt = Date()
    }
}

// MARK: - Location Data Model
@Model
final class LocationData {
    var id: UUID
    var latitude: Double
    var longitude: Double
    var altitude: Double
    var accuracy: Double
    var locationName: String
    var countryCode: String?
    var timeZoneIdentifier: String
    var lastUpdated: Date
    var isActive: Bool
    
    // Computed property for CLLocation
    var clLocation: CLLocation {
        CLLocation(
            coordinate: CLLocationCoordinate2D(latitude: latitude, longitude: longitude),
            altitude: altitude,
            horizontalAccuracy: accuracy,
            verticalAccuracy: accuracy,
            timestamp: lastUpdated
        )
    }
    
    // Computed property for Adhan Coordinates
    var coordinates: Coordinates {
        Coordinates(latitude: latitude, longitude: longitude)
    }
    
    init(
        location: CLLocation,
        locationName: String = "Unknown Location",
        countryCode: String? = nil,
        timeZoneIdentifier: String = TimeZone.current.identifier
    ) {
        self.id = UUID()
        self.latitude = location.coordinate.latitude
        self.longitude = location.coordinate.longitude
        self.altitude = location.altitude
        self.accuracy = location.horizontalAccuracy
        self.locationName = locationName
        self.countryCode = countryCode
        self.timeZoneIdentifier = timeZoneIdentifier
        self.lastUpdated = Date()
        self.isActive = true
    }
    
    func updateLocation(_ location: CLLocation, name: String? = nil) {
        self.latitude = location.coordinate.latitude
        self.longitude = location.coordinate.longitude
        self.altitude = location.altitude
        self.accuracy = location.horizontalAccuracy
        if let name = name {
            self.locationName = name
        }
        self.lastUpdated = Date()
    }
    
    func distanceFrom(_ location: CLLocation) -> Double {
        return clLocation.distance(from: location)
    }
}

// MARK: - Prayer Time Cache Model
@Model
final class PrayerTimeCache {
    var id: UUID
    var date: Date
    var locationId: UUID
    var fajrTime: Date
    var sunriseTime: Date
    var dhuhrTime: Date
    var asrTime: Date
    var maghribTime: Date
    var ishaTime: Date
    var calculationMethodRawValue: String
    var madhabRawValue: Int
    var createdAt: Date
    var expiresAt: Date
    
    // Computed properties
    var calculationMethod: CalculationMethod {
        get { CalculationMethod(rawValue: calculationMethodRawValue) ?? .turkey }
        set { calculationMethodRawValue = newValue.rawValue }
    }
    
    var madhab: Madhab {
        get { Madhab(rawValue: madhabRawValue) ?? .shafi }
        set { madhabRawValue = newValue.rawValue }
    }
    
    var isExpired: Bool {
        Date() > expiresAt
    }
    
    init(
        date: Date,
        locationId: UUID,
        prayerTimes: PrayerTimes,
        calculationMethod: CalculationMethod,
        madhab: Madhab
    ) {
        self.id = UUID()
        self.date = date
        self.locationId = locationId
        self.fajrTime = prayerTimes.fajr
        self.sunriseTime = prayerTimes.sunrise
        self.dhuhrTime = prayerTimes.dhuhr
        self.asrTime = prayerTimes.asr
        self.maghribTime = prayerTimes.maghrib
        self.ishaTime = prayerTimes.isha
        self.calculationMethodRawValue = calculationMethod.rawValue
        self.madhabRawValue = madhab.rawValue
        self.createdAt = Date()
        self.expiresAt = Calendar.current.date(byAdding: .hour, value: AppConstants.PrayerTime.cacheExpirationHours, to: Date()) ?? Date()
    }
    
    func toPrayerTimes() -> PrayerTimes? {
        // Create a mock PrayerTimes object with cached data
        // Note: This is a simplified approach. In a real implementation,
        // you might need to store more detailed calculation parameters
        let dateComponents = Calendar.current.dateComponents([.year, .month, .day], from: date)
        
        // This is a workaround since PrayerTimes doesn't have a direct initializer with times
        // In practice, you might need to recalculate or store the full PrayerTimes object differently
        return nil // Placeholder - would need custom implementation
    }
}

// MARK: - Notification History Model
@Model
final class NotificationHistory {
    var id: UUID
    var prayerType: String
    var scheduledTime: Date
    var actualDeliveryTime: Date?
    var wasDelivered: Bool
    var isBefore: Bool
    var beforeMinutes: Int?
    var locationId: UUID
    var createdAt: Date
    
    var prayer: Prayer? {
        Prayer.allCases.first { $0.displayName == prayerType }
    }
    
    init(
        prayer: Prayer,
        scheduledTime: Date,
        isBefore: Bool = false,
        beforeMinutes: Int? = nil,
        locationId: UUID
    ) {
        self.id = UUID()
        self.prayerType = prayer.displayName
        self.scheduledTime = scheduledTime
        self.actualDeliveryTime = nil
        self.wasDelivered = false
        self.isBefore = isBefore
        self.beforeMinutes = beforeMinutes
        self.locationId = locationId
        self.createdAt = Date()
    }
    
    func markAsDelivered() {
        self.wasDelivered = true
        self.actualDeliveryTime = Date()
    }
}

// MARK: - App Configuration Model
@Model
final class AppConfiguration {
    var id: UUID
    var version: String
    var lastUpdateCheck: Date
    var features: [String: Bool]
    var apiConfiguration: Data? // Encoded API settings
    var createdAt: Date
    var updatedAt: Date
    
    init(version: String = AppConstants.App.version) {
        self.id = UUID()
        self.version = version
        self.lastUpdateCheck = Date()
        self.features = [:]
        self.apiConfiguration = nil
        self.createdAt = Date()
        self.updatedAt = Date()
    }
    
    func updateFeature(_ feature: String, enabled: Bool) {
        features[feature] = enabled
        updatedAt = Date()
    }
    
    func isFeatureEnabled(_ feature: String) -> Bool {
        return features[feature] ?? false
    }
}

// MARK: - Extensions for AppConstants
extension AppConstants.App {
    static let defaultNotificationSettings: [String: Bool] = [
        "Fajr": true,
        "Sunrise": false,
        "Dhuhr": true,
        "Asr": true,
        "Maghrib": true,
        "Isha": true
    ]
    
    static let defaultBeforeNotificationSettings: [String: Bool] = [
        "Fajr": false,
        "Sunrise": false,
        "Dhuhr": false,
        "Asr": false,
        "Maghrib": false,
        "Isha": false
    ]
}
