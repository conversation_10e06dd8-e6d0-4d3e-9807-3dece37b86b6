//
//  SwiftDataRepository.swift
//  PrayerEase
//
//  Created by <PERSON><PERSON><PERSON> on 12/24/24.
//

import Foundation
import SwiftData
import CoreLocation
import Adhan

// MARK: - SwiftData Repository Implementation
@MainActor
final class SwiftDataRepository: DataRepositoryProtocol {
    let modelContainer: ModelContainer
    private let modelContext: ModelContext
    
    init() throws {
        let schema = Schema([
            UserSettings.self,
            LocationData.self,
            PrayerTimeCache.self,
            NotificationHistory.self,
            AppConfiguration.self
        ])
        
        let modelConfiguration = ModelConfiguration(
            schema: schema,
            isStoredInMemoryOnly: false,
            allowsSave: true
        )
        
        do {
            self.modelContainer = try ModelContainer(
                for: schema,
                configurations: [modelConfiguration]
            )
            self.modelContext = modelContainer.mainContext
        } catch {
            throw DataError.initializationFailed(error)
        }
    }
    
    // MARK: - Generic CRUD Operations
    func save<T: Codable>(_ object: T, forKey key: String) throws {
        // This method is kept for protocol compliance but SwiftData uses different patterns
        // For SwiftData, we use specific save methods for each model type
        throw DataError.operationNotSupported("Use specific save methods for SwiftData models")
    }
    
    func load<T: Codable>(_ type: T.Type, forKey key: String) throws -> T? {
        // This method is kept for protocol compliance but SwiftData uses different patterns
        // For SwiftData, we use specific fetch methods for each model type
        throw DataError.operationNotSupported("Use specific fetch methods for SwiftData models")
    }
    
    func delete(forKey key: String) throws {
        // This method is kept for protocol compliance but SwiftData uses different patterns
        throw DataError.operationNotSupported("Use specific delete methods for SwiftData models")
    }
    
    func exists(forKey key: String) -> Bool {
        // This method is kept for protocol compliance but SwiftData uses different patterns
        return false
    }
    
    // MARK: - User Settings Operations
    func saveUserSettings(_ settings: UserSettings) throws {
        modelContext.insert(settings)
        try saveContext()
    }
    
    func fetchUserSettings() throws -> UserSettings? {
        let descriptor = FetchDescriptor<UserSettings>(
            sortBy: [SortDescriptor(\.updatedAt, order: .reverse)]
        )
        let settings = try modelContext.fetch(descriptor)
        return settings.first
    }
    
    func updateUserSettings(_ settings: UserSettings) throws {
        settings.updateTimestamp()
        try saveContext()
    }
    
    // MARK: - Location Data Operations
    func saveLocationData(_ locationData: LocationData) throws {
        // Deactivate previous active locations
        let activeLocations = try fetchActiveLocationData()
        for location in activeLocations {
            location.isActive = false
        }
        
        modelContext.insert(locationData)
        try saveContext()
    }
    
    func fetchActiveLocationData() throws -> [LocationData] {
        let descriptor = FetchDescriptor<LocationData>(
            predicate: #Predicate { $0.isActive == true },
            sortBy: [SortDescriptor(\.lastUpdated, order: .reverse)]
        )
        return try modelContext.fetch(descriptor)
    }
    
    func fetchLocationData(limit: Int = 10) throws -> [LocationData] {
        let descriptor = FetchDescriptor<LocationData>(
            sortBy: [SortDescriptor(\.lastUpdated, order: .reverse)]
        )
        descriptor.fetchLimit = limit
        return try modelContext.fetch(descriptor)
    }
    
    // MARK: - Prayer Time Cache Operations
    func savePrayerTimeCache(_ cache: PrayerTimeCache) throws {
        modelContext.insert(cache)
        try saveContext()
    }
    
    func fetchPrayerTimeCache(
        for date: Date,
        locationId: UUID,
        calculationMethod: CalculationMethod,
        madhab: Madhab
    ) throws -> PrayerTimeCache? {
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: date)
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay) ?? date
        
        let descriptor = FetchDescriptor<PrayerTimeCache>(
            predicate: #Predicate<PrayerTimeCache> { cache in
                cache.date >= startOfDay &&
                cache.date < endOfDay &&
                cache.locationId == locationId &&
                cache.calculationMethodRawValue == calculationMethod.rawValue &&
                cache.madhabRawValue == madhab.rawValue &&
                cache.expiresAt > Date()
            }
        )
        
        let caches = try modelContext.fetch(descriptor)
        return caches.first
    }
    
    func cleanExpiredPrayerTimeCache() throws {
        let descriptor = FetchDescriptor<PrayerTimeCache>(
            predicate: #Predicate { $0.expiresAt <= Date() }
        )
        
        let expiredCaches = try modelContext.fetch(descriptor)
        for cache in expiredCaches {
            modelContext.delete(cache)
        }
        
        try saveContext()
    }
    
    // MARK: - Notification History Operations
    func saveNotificationHistory(_ history: NotificationHistory) throws {
        modelContext.insert(history)
        try saveContext()
    }
    
    func fetchNotificationHistory(limit: Int = 100) throws -> [NotificationHistory] {
        let descriptor = FetchDescriptor<NotificationHistory>(
            sortBy: [SortDescriptor(\.createdAt, order: .reverse)]
        )
        descriptor.fetchLimit = limit
        return try modelContext.fetch(descriptor)
    }
    
    func markNotificationAsDelivered(id: UUID) throws {
        let descriptor = FetchDescriptor<NotificationHistory>(
            predicate: #Predicate { $0.id == id }
        )
        
        if let notification = try modelContext.fetch(descriptor).first {
            notification.markAsDelivered()
            try saveContext()
        }
    }
    
    // MARK: - App Configuration Operations
    func saveAppConfiguration(_ config: AppConfiguration) throws {
        modelContext.insert(config)
        try saveContext()
    }
    
    func fetchAppConfiguration() throws -> AppConfiguration? {
        let descriptor = FetchDescriptor<AppConfiguration>(
            sortBy: [SortDescriptor(\.updatedAt, order: .reverse)]
        )
        let configs = try modelContext.fetch(descriptor)
        return configs.first
    }
    
    // MARK: - Utility Methods
    private func saveContext() throws {
        do {
            try modelContext.save()
        } catch {
            throw DataError.saveFailed(error)
        }
    }
    
    func deleteAll() throws {
        // Delete all data - useful for testing or reset functionality
        try modelContext.delete(model: UserSettings.self)
        try modelContext.delete(model: LocationData.self)
        try modelContext.delete(model: PrayerTimeCache.self)
        try modelContext.delete(model: NotificationHistory.self)
        try modelContext.delete(model: AppConfiguration.self)
        
        try saveContext()
    }
    
    func performMaintenance() throws {
        // Clean up expired data
        try cleanExpiredPrayerTimeCache()
        
        // Clean up old notification history (keep only last 30 days)
        let thirtyDaysAgo = Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date()
        let descriptor = FetchDescriptor<NotificationHistory>(
            predicate: #Predicate { $0.createdAt < thirtyDaysAgo }
        )
        
        let oldNotifications = try modelContext.fetch(descriptor)
        for notification in oldNotifications {
            modelContext.delete(notification)
        }
        
        try saveContext()
    }
}

// MARK: - Data Errors
enum DataError: LocalizedError {
    case initializationFailed(Error)
    case saveFailed(Error)
    case fetchFailed(Error)
    case deleteFailed(Error)
    case operationNotSupported(String)
    case dataCorrupted
    
    var errorDescription: String? {
        switch self {
        case .initializationFailed(let error):
            return "Failed to initialize data storage: \(error.localizedDescription)"
        case .saveFailed(let error):
            return "Failed to save data: \(error.localizedDescription)"
        case .fetchFailed(let error):
            return "Failed to fetch data: \(error.localizedDescription)"
        case .deleteFailed(let error):
            return "Failed to delete data: \(error.localizedDescription)"
        case .operationNotSupported(let message):
            return "Operation not supported: \(message)"
        case .dataCorrupted:
            return "Data is corrupted and cannot be read"
        }
    }
}
