//
//  Protocols.swift
//  PrayerEase
//
//  Created by <PERSON><PERSON><PERSON> on 12/24/24.
//

import Foundation
import CoreLocation
import Adhan
import UserNotifications

// MARK: - Location Service Protocol
@MainActor
protocol LocationServiceProtocol: ObservableObject {
    var currentLocation: CLLocation? { get }
    var locationName: String { get }
    var isLocationAuthorized: Bool { get }
    var heading: Double { get }
    var headingAccuracy: Double { get }
    var error: LocationError? { get }
    
    func requestLocationPermission() async
    func startLocationUpdates() async
    func stopLocationUpdates()
    func startHeadingUpdates()
    func stopHeadingUpdates()
    func calculateQiblaDirection() -> Double?
}

// MARK: - Prayer Time Service Protocol
@MainActor
protocol PrayerTimeServiceProtocol: ObservableObject {
    var currentPrayerTimes: PrayerTimes? { get }
    var calculationMethod: CalculationMethod { get set }
    var madhab: Madhab { get set }
    var nextPrayer: Prayer? { get }
    var timeUntilNextPrayer: TimeInterval? { get }
    
    func calculatePrayerTimes(for date: Date, location: CLLocation) async throws -> PrayerTimes
    func getMonthlyPrayerTimes(for date: Date, location: CLLocation) async throws -> [PrayerTimes]
    func getCurrentPrayer() -> Prayer?
    func getNextPrayer() -> Prayer?
}

// MARK: - Notification Service Protocol
@MainActor
protocol NotificationServiceProtocol: ObservableObject {
    var isAuthorized: Bool { get }
    var notificationSettings: [Prayer: Bool] { get set }
    var beforeNotificationSettings: [Prayer: Bool] { get set }
    var beforeMinutes: Int { get set }
    
    func requestNotificationPermission() async -> Bool
    func scheduleNotifications(for prayerTimes: [PrayerTimes]) async throws
    func cancelAllNotifications()
    func updateNotificationSettings(for prayer: Prayer, enabled: Bool, isBefore: Bool)
}

// MARK: - Data Repository Protocol
protocol DataRepositoryProtocol {
    func save<T: Codable>(_ object: T, forKey key: String) throws
    func load<T: Codable>(_ type: T.Type, forKey key: String) throws -> T?
    func delete(forKey key: String) throws
    func exists(forKey key: String) -> Bool
}

// MARK: - Configuration Protocol
protocol ConfigurationProtocol {
    var apiKeys: APIKeys { get }
    var calculationMethods: [CalculationMethod] { get }
    var madhabOptions: [Madhab] { get }
    var defaultSettings: AppSettings { get }
    var notificationSettings: NotificationConfiguration { get }
}

// MARK: - Error Types
enum LocationError: LocalizedError {
    case permissionDenied
    case locationUnavailable
    case networkError
    case unknown(Error)
    
    var errorDescription: String? {
        switch self {
        case .permissionDenied:
            return "Location permission denied. Please enable location access in Settings."
        case .locationUnavailable:
            return "Unable to determine your location. Please try again."
        case .networkError:
            return "Network error occurred while fetching location data."
        case .unknown(let error):
            return "An unexpected error occurred: \(error.localizedDescription)"
        }
    }
}

enum PrayerTimeError: LocalizedError {
    case invalidLocation
    case calculationFailed
    case dataCorrupted
    case unknown(Error)
    
    var errorDescription: String? {
        switch self {
        case .invalidLocation:
            return "Invalid location provided for prayer time calculation."
        case .calculationFailed:
            return "Failed to calculate prayer times. Please try again."
        case .dataCorrupted:
            return "Prayer time data is corrupted. Please reset the app."
        case .unknown(let error):
            return "An unexpected error occurred: \(error.localizedDescription)"
        }
    }
}

enum NotificationError: LocalizedError {
    case permissionDenied
    case schedulingFailed
    case invalidPrayerTime
    case unknown(Error)
    
    var errorDescription: String? {
        switch self {
        case .permissionDenied:
            return "Notification permission denied. Please enable notifications in Settings."
        case .schedulingFailed:
            return "Failed to schedule prayer notifications."
        case .invalidPrayerTime:
            return "Invalid prayer time provided for notification."
        case .unknown(let error):
            return "An unexpected error occurred: \(error.localizedDescription)"
        }
    }
}

// MARK: - Data Models
struct APIKeys: Codable {
    let openAI: String
    
    static let `default` = APIKeys(
        openAI: "" // Will be loaded from secure storage or environment
    )
}

struct AppSettings: Codable {
    let calculationMethod: CalculationMethod
    let madhab: Madhab
    let notificationSettings: [String: Bool]
    let beforeNotificationSettings: [String: Bool]
    let beforeMinutes: Int
    
    static let `default` = AppSettings(
        calculationMethod: .turkey,
        madhab: .shafi,
        notificationSettings: [
            "Fajr": true,
            "Sunrise": false,
            "Dhuhr": true,
            "Asr": true,
            "Maghrib": true,
            "Isha": true
        ],
        beforeNotificationSettings: [
            "Fajr": false,
            "Sunrise": false,
            "Dhuhr": false,
            "Asr": false,
            "Maghrib": false,
            "Isha": false
        ],
        beforeMinutes: 15
    )
}

struct NotificationConfiguration: Codable {
    let maxDaysAhead: Int
    let backgroundTaskIdentifier: String
    let soundEnabled: Bool
    let badgeEnabled: Bool
    
    static let `default` = NotificationConfiguration(
        maxDaysAhead: 7,
        backgroundTaskIdentifier: "com.alijaver.PrayerEase.refreshNotifications",
        soundEnabled: true,
        badgeEnabled: true
    )
}

// MARK: - Prayer Extensions
extension Prayer: CaseIterable {
    public static var allCases: [Prayer] {
        return [.fajr, .sunrise, .dhuhr, .asr, .maghrib, .isha]
    }
    
    var displayName: String {
        switch self {
        case .fajr: return "Fajr"
        case .sunrise: return "Sunrise"
        case .dhuhr: return "Dhuhr"
        case .asr: return "Asr"
        case .maghrib: return "Maghrib"
        case .isha: return "Isha"
        }
    }
    
    var systemImageName: String {
        switch self {
        case .fajr: return "sunrise"
        case .sunrise: return "sun.and.horizon"
        case .dhuhr: return "sun.max"
        case .asr: return "sunset"
        case .maghrib: return "moon"
        case .isha: return "moon.stars"
        }
    }
}

// MARK: - Calculation Method Extensions
extension CalculationMethod: CaseIterable {
    public static var allCases: [CalculationMethod] {
        return [.muslimWorldLeague, .egyptian, .karachi, .ummAlQura, .dubai, .moonsightingCommittee, .northAmerica, .kuwait, .qatar, .singapore, .tehran, .turkey]
    }
    
    var displayName: String {
        switch self {
        case .muslimWorldLeague: return "Muslim World League"
        case .egyptian: return "Egyptian General Authority"
        case .karachi: return "University of Islamic Sciences, Karachi"
        case .ummAlQura: return "Umm Al-Qura University, Makkah"
        case .dubai: return "Dubai"
        case .moonsightingCommittee: return "Moonsighting Committee Worldwide"
        case .northAmerica: return "Islamic Society of North America (ISNA)"
        case .kuwait: return "Kuwait"
        case .qatar: return "Qatar"
        case .singapore: return "Singapore"
        case .tehran: return "Institute of Geophysics, University of Tehran"
        case .turkey: return "Turkey"
        case .other: return "Custom"
        }
    }
}
