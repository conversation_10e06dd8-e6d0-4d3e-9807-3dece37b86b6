//
//  ConfigurationService.swift
//  PrayerEase
//
//  Created by <PERSON><PERSON><PERSON> on 12/24/24.
//

import Foundation
import Adhan

// MARK: - Configuration Service
@MainActor
final class ConfigurationService: ConfigurationProtocol {
    
    // MARK: - Singleton
    static let shared = ConfigurationService()
    
    // MARK: - Properties
    private(set) var apiKeys: APIKeys
    private(set) var calculationMethods: [CalculationMethod]
    private(set) var madhabOptions: [Madhab]
    private(set) var defaultSettings: AppSettings
    private(set) var notificationSettings: NotificationConfiguration
    
    private let repository: SwiftDataRepository?
    
    // MARK: - Initialization
    private init() {
        // Initialize with default values
        self.apiKeys = APIKeys.default
        self.calculationMethods = CalculationMethod.allCases
        self.madhabOptions = [.hanafi, .shafi]
        self.defaultSettings = AppSettings.default
        self.notificationSettings = NotificationConfiguration.default
        
        // Try to initialize repository
        do {
            self.repository = try SwiftDataRepository()
            loadConfiguration()
        } catch {
            print("Failed to initialize repository in ConfigurationService: \(error)")
            self.repository = nil
        }
    }
    
    // MARK: - Configuration Loading
    private func loadConfiguration() {
        Task {
            await loadAPIKeys()
            await loadAppConfiguration()
        }
    }
    
    private func loadAPIKeys() async {
        // Load API keys from secure storage or environment
        if let openAIKey = loadSecureValue(for: "OPENAI_API_KEY") {
            self.apiKeys = APIKeys(openAI: openAIKey)
        } else {
            // Fallback to environment or plist
            self.apiKeys = loadAPIKeysFromEnvironment()
        }
    }
    
    private func loadAPIKeysFromEnvironment() -> APIKeys {
        // Check environment variables first
        if let openAIKey = ProcessInfo.processInfo.environment["OPENAI_API_KEY"] {
            return APIKeys(openAI: openAIKey)
        }
        
        // Check Info.plist
        if let path = Bundle.main.path(forResource: "APIKeys", ofType: "plist"),
           let plist = NSDictionary(contentsOfFile: path),
           let openAIKey = plist["OPENAI_API_KEY"] as? String {
            return APIKeys(openAI: openAIKey)
        }
        
        // Return default (empty) if not found
        return APIKeys.default
    }
    
    private func loadAppConfiguration() async {
        guard let repository = repository else { return }
        
        do {
            if let config = try repository.fetchAppConfiguration() {
                // Update feature flags and other configuration
                updateFeatureFlags(from: config)
            }
        } catch {
            print("Failed to load app configuration: \(error)")
        }
    }
    
    private func updateFeatureFlags(from config: AppConfiguration) {
        // Update any dynamic configuration based on stored settings
        // This could include feature flags, API endpoints, etc.
    }
    
    // MARK: - Secure Storage
    private func loadSecureValue(for key: String) -> String? {
        // Implementation would use Keychain Services
        // For now, return nil to use fallback methods
        return nil
    }
    
    private func saveSecureValue(_ value: String, for key: String) -> Bool {
        // Implementation would use Keychain Services
        return false
    }
    
    // MARK: - Dynamic Configuration Updates
    func updateAPIKey(_ key: String, for service: String) async -> Bool {
        switch service.lowercased() {
        case "openai":
            if saveSecureValue(key, for: "OPENAI_API_KEY") {
                self.apiKeys = APIKeys(openAI: key)
                return true
            }
        default:
            break
        }
        return false
    }
    
    func updateCalculationMethods(_ methods: [CalculationMethod]) {
        self.calculationMethods = methods
        saveConfiguration()
    }
    
    func updateDefaultSettings(_ settings: AppSettings) {
        self.defaultSettings = settings
        saveConfiguration()
    }
    
    func updateNotificationConfiguration(_ config: NotificationConfiguration) {
        self.notificationSettings = config
        saveConfiguration()
    }
    
    private func saveConfiguration() {
        Task {
            guard let repository = repository else { return }
            
            do {
                var config = try repository.fetchAppConfiguration() ?? AppConfiguration()
                
                // Update configuration with current settings
                config.updateFeature("chat_enabled", enabled: AppConstants.FeatureFlags.enableChatFeature)
                config.updateFeature("advanced_notifications", enabled: AppConstants.FeatureFlags.enableAdvancedNotifications)
                config.updateFeature("location_caching", enabled: AppConstants.FeatureFlags.enableLocationCaching)
                
                if config.id == UUID() {
                    try repository.saveAppConfiguration(config)
                } else {
                    config.updatedAt = Date()
                    // SwiftData automatically saves changes
                }
            } catch {
                print("Failed to save configuration: \(error)")
            }
        }
    }
    
    // MARK: - Feature Flags
    func isFeatureEnabled(_ feature: String) -> Bool {
        switch feature.lowercased() {
        case "chat":
            return AppConstants.FeatureFlags.enableChatFeature
        case "advanced_notifications":
            return AppConstants.FeatureFlags.enableAdvancedNotifications
        case "location_caching":
            return AppConstants.FeatureFlags.enableLocationCaching
        case "analytics":
            return AppConstants.FeatureFlags.enableAnalytics
        case "debug":
            return AppConstants.FeatureFlags.enableDebugMode
        default:
            return false
        }
    }
    
    func enableFeature(_ feature: String, enabled: Bool) {
        // This would update the feature flag and save to configuration
        saveConfiguration()
    }
    
    // MARK: - Validation
    func validateConfiguration() -> [String] {
        var issues: [String] = []
        
        // Validate API keys
        if apiKeys.openAI.isEmpty && isFeatureEnabled("chat") {
            issues.append("OpenAI API key is missing but chat feature is enabled")
        }
        
        // Validate calculation methods
        if calculationMethods.isEmpty {
            issues.append("No calculation methods available")
        }
        
        // Validate madhab options
        if madhabOptions.isEmpty {
            issues.append("No madhab options available")
        }
        
        // Validate notification settings
        if notificationSettings.maxDaysAhead <= 0 {
            issues.append("Invalid notification max days ahead setting")
        }
        
        return issues
    }
    
    // MARK: - Environment-Specific Configuration
    func getEnvironmentConfiguration() -> EnvironmentConfiguration {
        switch Environment.current {
        case .development:
            return EnvironmentConfiguration(
                enableLogging: true,
                enableDebugFeatures: true,
                apiTimeout: 60.0,
                maxRetries: 5
            )
        case .staging:
            return EnvironmentConfiguration(
                enableLogging: true,
                enableDebugFeatures: false,
                apiTimeout: 30.0,
                maxRetries: 3
            )
        case .production:
            return EnvironmentConfiguration(
                enableLogging: false,
                enableDebugFeatures: false,
                apiTimeout: 30.0,
                maxRetries: 3
            )
        }
    }
    
    // MARK: - Localization Support
    func getSupportedLanguages() -> [String] {
        return ["en", "ar", "tr", "ur", "id", "ms"] // Common languages for Islamic apps
    }
    
    func getLocalizedCalculationMethodName(_ method: CalculationMethod) -> String {
        // This would return localized names based on current locale
        return method.displayName
    }
    
    // MARK: - Reset Configuration
    func resetToDefaults() async {
        self.apiKeys = APIKeys.default
        self.calculationMethods = CalculationMethod.allCases
        self.madhabOptions = [.hanafi, .shafi]
        self.defaultSettings = AppSettings.default
        self.notificationSettings = NotificationConfiguration.default
        
        // Clear stored configuration
        guard let repository = repository else { return }
        
        do {
            try repository.deleteAll()
        } catch {
            print("Failed to reset configuration: \(error)")
        }
    }
}

// MARK: - Environment Configuration
struct EnvironmentConfiguration {
    let enableLogging: Bool
    let enableDebugFeatures: Bool
    let apiTimeout: TimeInterval
    let maxRetries: Int
}

// MARK: - Configuration Extensions
extension ConfigurationService {
    
    // MARK: - Prayer Time Configuration
    func getPrayerTimeConfiguration() -> PrayerTimeConfiguration {
        return PrayerTimeConfiguration(
            defaultMethod: defaultSettings.calculationMethod,
            defaultMadhab: defaultSettings.madhab,
            cacheExpirationHours: AppConstants.PrayerTime.cacheExpirationHours,
            maxDaysAhead: AppConstants.PrayerTime.maxDaysAhead
        )
    }
    
    // MARK: - Location Configuration
    func getLocationConfiguration() -> LocationConfiguration {
        return LocationConfiguration(
            desiredAccuracy: AppConstants.Location.desiredAccuracy,
            distanceFilter: AppConstants.Location.distanceFilter,
            maxLocationAge: AppConstants.Location.maxLocationAge,
            qiblaAccuracyThreshold: AppConstants.Location.qiblaAccuracyThreshold
        )
    }
    
    // MARK: - UI Configuration
    func getUIConfiguration() -> UIConfiguration {
        return UIConfiguration(
            animationDuration: AppConstants.UI.animationDuration,
            cornerRadius: AppConstants.UI.cornerRadius,
            shadowRadius: AppConstants.UI.shadowRadius,
            shadowOpacity: AppConstants.UI.shadowOpacity,
            minimumTapTargetSize: AppConstants.UI.minimumTapTargetSize
        )
    }
}

// MARK: - Configuration Structs
struct PrayerTimeConfiguration {
    let defaultMethod: CalculationMethod
    let defaultMadhab: Madhab
    let cacheExpirationHours: Int
    let maxDaysAhead: Int
}

struct LocationConfiguration {
    let desiredAccuracy: Double
    let distanceFilter: Double
    let maxLocationAge: TimeInterval
    let qiblaAccuracyThreshold: Double
}

struct UIConfiguration {
    let animationDuration: Double
    let cornerRadius: Double
    let shadowRadius: Double
    let shadowOpacity: Float
    let minimumTapTargetSize: Double
}
