//
//  LocationService.swift
//  PrayerEase
//
//  Created by <PERSON><PERSON><PERSON> on 12/24/24.
//

import Foundation
import CoreLocation
import Adhan
import Combine

// MARK: - Modern Location Service with Async/Await
@MainActor
final class LocationService: NSObject, LocationServiceProtocol {
    
    // MARK: - Published Properties
    @Published private(set) var currentLocation: CLLocation?
    @Published private(set) var locationName: String = "Unknown Location"
    @Published private(set) var isLocationAuthorized: Bool = false
    @Published private(set) var heading: Double = 0.0
    @Published private(set) var headingAccuracy: Double = 0.0
    @Published private(set) var error: LocationError?
    
    // MARK: - Private Properties
    private let locationManager = CLLocationManager()
    private let geocoder = CLGeocoder()
    private let repository: SwiftDataRepository
    
    private var locationContinuation: CheckedContinuation<CLLocation, Error>?
    private var permissionContinuation: CheckedContinuation<Bool, Never>?
    
    private var lastLocationUpdate: Date?
    private var isUpdatingLocation = false
    private var isUpdatingHeading = false
    
    // MARK: - Initialization
    init(repository: SwiftDataRepository) {
        self.repository = repository
        super.init()
        setupLocationManager()
        loadCachedLocation()
    }
    
    // MARK: - Setup
    private func setupLocationManager() {
        locationManager.delegate = self
        locationManager.desiredAccuracy = AppConstants.Location.desiredAccuracy
        locationManager.distanceFilter = AppConstants.Location.distanceFilter
        
        // Check initial authorization status
        updateAuthorizationStatus(locationManager.authorizationStatus)
    }
    
    private func loadCachedLocation() {
        Task {
            do {
                let activeLocations = try repository.fetchActiveLocationData()
                if let cachedLocation = activeLocations.first,
                   let lastUpdate = lastLocationUpdate,
                   Date().timeIntervalSince(lastUpdate) < AppConstants.Location.maxLocationAge {
                    
                    self.currentLocation = cachedLocation.clLocation
                    self.locationName = cachedLocation.locationName
                    
                    // Reverse geocode if name is generic
                    if cachedLocation.locationName == "Unknown Location" {
                        await reverseGeocodeLocation(cachedLocation.clLocation)
                    }
                }
            } catch {
                print("Failed to load cached location: \(error)")
            }
        }
    }
    
    // MARK: - Public Methods
    func requestLocationPermission() async {
        await withCheckedContinuation { continuation in
            self.permissionContinuation = continuation
            
            switch locationManager.authorizationStatus {
            case .notDetermined:
                locationManager.requestWhenInUseAuthorization()
            case .denied, .restricted:
                self.error = .permissionDenied
                continuation.resume(returning: false)
                self.permissionContinuation = nil
            case .authorizedWhenInUse, .authorizedAlways:
                continuation.resume(returning: true)
                self.permissionContinuation = nil
            @unknown default:
                continuation.resume(returning: false)
                self.permissionContinuation = nil
            }
        }
    }
    
    func startLocationUpdates() async {
        guard !isUpdatingLocation else { return }
        
        // Check if we have recent location data
        if let lastUpdate = lastLocationUpdate,
           Date().timeIntervalSince(lastUpdate) < AppConstants.PrayerTime.minimumTimeBetweenUpdates,
           currentLocation != nil {
            return
        }
        
        isUpdatingLocation = true
        error = nil
        
        do {
            let location = try await requestSingleLocation()
            await handleNewLocation(location)
        } catch {
            await handleLocationError(error)
        }
        
        isUpdatingLocation = false
    }
    
    func stopLocationUpdates() {
        locationManager.stopUpdatingLocation()
        locationContinuation?.resume(throwing: LocationError.locationUnavailable)
        locationContinuation = nil
        isUpdatingLocation = false
    }
    
    func startHeadingUpdates() {
        guard CLLocationManager.headingAvailable() else {
            error = .locationUnavailable
            return
        }
        
        guard !isUpdatingHeading else { return }
        
        isUpdatingHeading = true
        locationManager.startUpdatingHeading()
    }
    
    func stopHeadingUpdates() {
        locationManager.stopUpdatingHeading()
        isUpdatingHeading = false
    }
    
    func calculateQiblaDirection() -> Double? {
        guard let location = currentLocation else { return nil }
        
        let coordinates = Coordinates(
            latitude: location.coordinate.latitude,
            longitude: location.coordinate.longitude
        )
        
        return Qibla(coordinates: coordinates).direction
    }
    
    // MARK: - Private Methods
    private func requestSingleLocation() async throws -> CLLocation {
        guard isLocationAuthorized else {
            throw LocationError.permissionDenied
        }
        
        return try await withCheckedThrowingContinuation { continuation in
            self.locationContinuation = continuation
            
            // Use one-time location request for better battery efficiency
            if #available(iOS 14.0, *) {
                locationManager.requestLocation()
            } else {
                locationManager.startUpdatingLocation()
            }
            
            // Set a timeout
            DispatchQueue.main.asyncAfter(deadline: .now() + 30) {
                if self.locationContinuation != nil {
                    self.locationContinuation?.resume(throwing: LocationError.locationUnavailable)
                    self.locationContinuation = nil
                    self.locationManager.stopUpdatingLocation()
                }
            }
        }
    }
    
    private func handleNewLocation(_ location: CLLocation) async {
        // Validate location accuracy
        guard location.horizontalAccuracy <= AppConstants.Location.desiredAccuracy else {
            return
        }
        
        // Check if location has changed significantly
        if let currentLocation = currentLocation,
           currentLocation.distance(from: location) < AppConstants.Location.distanceFilter {
            return
        }
        
        self.currentLocation = location
        self.lastLocationUpdate = Date()
        
        // Reverse geocode to get location name
        await reverseGeocodeLocation(location)
        
        // Save to repository
        await saveLocationData(location)
    }
    
    private func reverseGeocodeLocation(_ location: CLLocation) async {
        do {
            let placemarks = try await geocoder.reverseGeocodeLocation(location)
            
            if let placemark = placemarks.first {
                let name = placemark.locality ??
                          placemark.administrativeArea ??
                          placemark.country ??
                          "Unknown Location"
                
                self.locationName = name
            }
        } catch {
            print("Reverse geocoding failed: \(error)")
            // Keep the current name or set to coordinates
            if locationName == "Unknown Location" {
                locationName = String(format: "%.4f, %.4f", location.coordinate.latitude, location.coordinate.longitude)
            }
        }
    }
    
    private func saveLocationData(_ location: CLLocation) async {
        do {
            let locationData = LocationData(
                location: location,
                locationName: locationName,
                timeZoneIdentifier: TimeZone.current.identifier
            )
            
            try repository.saveLocationData(locationData)
        } catch {
            print("Failed to save location data: \(error)")
        }
    }
    
    private func handleLocationError(_ error: Error) async {
        if let clError = error as? CLError {
            switch clError.code {
            case .denied:
                self.error = .permissionDenied
            case .network:
                self.error = .networkError
            case .locationUnknown:
                self.error = .locationUnavailable
            default:
                self.error = .unknown(error)
            }
        } else {
            self.error = .unknown(error)
        }
    }
    
    private func updateAuthorizationStatus(_ status: CLAuthorizationStatus) {
        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            isLocationAuthorized = true
            error = nil
        case .denied, .restricted:
            isLocationAuthorized = false
            error = .permissionDenied
        case .notDetermined:
            isLocationAuthorized = false
            error = nil
        @unknown default:
            isLocationAuthorized = false
            error = .unknown(NSError(domain: "LocationService", code: -1, userInfo: [NSLocalizedDescriptionKey: "Unknown authorization status"]))
        }
    }
}

// MARK: - CLLocationManagerDelegate
extension LocationService: CLLocationManagerDelegate {
    
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }
        
        locationContinuation?.resume(returning: location)
        locationContinuation = nil
        
        // Stop updating if we got a good location
        if location.horizontalAccuracy <= AppConstants.Location.desiredAccuracy {
            manager.stopUpdatingLocation()
        }
        
        Task {
            await handleNewLocation(location)
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        locationContinuation?.resume(throwing: error)
        locationContinuation = nil
        
        Task {
            await handleLocationError(error)
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        updateAuthorizationStatus(status)
        
        permissionContinuation?.resume(returning: isLocationAuthorized)
        permissionContinuation = nil
    }
    
    func locationManager(_ manager: CLLocationManager, didUpdateHeading newHeading: CLHeading) {
        guard newHeading.headingAccuracy >= 0 else { return }
        
        heading = newHeading.trueHeading >= 0 ? newHeading.trueHeading : newHeading.magneticHeading
        headingAccuracy = newHeading.headingAccuracy
    }
}
