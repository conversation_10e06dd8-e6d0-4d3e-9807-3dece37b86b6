//
//  NotificationService.swift
//  PrayerEase
//
//  Created by <PERSON><PERSON><PERSON> on 12/24/24.
//

import Foundation
import UserNotifications
import BackgroundTasks
import Adhan
import CoreLocation

// MARK: - Advanced Notification Service
@MainActor
final class NotificationService: NSObject, NotificationServiceProtocol {
    
    // MARK: - Published Properties
    @Published private(set) var isAuthorized: Bool = false
    @Published var notificationSettings: [Prayer: Bool] = [:] {
        didSet { saveNotificationSettings() }
    }
    @Published var beforeNotificationSettings: [Prayer: Bool] = [:] {
        didSet { saveNotificationSettings() }
    }
    @Published var beforeMinutes: Int = AppConstants.Notifications.defaultBeforeMinutes {
        didSet { saveNotificationSettings() }
    }
    
    // MARK: - Private Properties
    private let notificationCenter = UNUserNotificationCenter.current()
    private let repository: SwiftDataRepository
    private var backgroundTaskID: UIBackgroundTaskIdentifier = .invalid
    
    // MARK: - Initialization
    init(repository: SwiftDataRepository) {
        self.repository = repository
        super.init()
        
        notificationCenter.delegate = self
        loadNotificationSettings()
        registerBackgroundTasks()
        
        Task {
            await checkAuthorizationStatus()
        }
    }
    
    // MARK: - Setup
    private func loadNotificationSettings() {
        Task {
            do {
                if let settings = try repository.fetchUserSettings() {
                    // Convert string keys to Prayer enum
                    var prayerSettings: [Prayer: Bool] = [:]
                    var beforePrayerSettings: [Prayer: Bool] = [:]
                    
                    for prayer in Prayer.allCases {
                        prayerSettings[prayer] = settings.notificationSettings[prayer.displayName] ?? true
                        beforePrayerSettings[prayer] = settings.beforeNotificationSettings[prayer.displayName] ?? false
                    }
                    
                    self.notificationSettings = prayerSettings
                    self.beforeNotificationSettings = beforePrayerSettings
                    self.beforeMinutes = settings.beforeMinutes
                }
            } catch {
                print("Failed to load notification settings: \(error)")
                setDefaultSettings()
            }
        }
    }
    
    private func setDefaultSettings() {
        notificationSettings = [
            .fajr: true,
            .sunrise: false,
            .dhuhr: true,
            .asr: true,
            .maghrib: true,
            .isha: true
        ]
        
        beforeNotificationSettings = [
            .fajr: false,
            .sunrise: false,
            .dhuhr: false,
            .asr: false,
            .maghrib: false,
            .isha: false
        ]
    }
    
    private func saveNotificationSettings() {
        Task {
            do {
                var settings = try repository.fetchUserSettings() ?? UserSettings()
                
                // Convert Prayer enum to string keys
                var stringSettings: [String: Bool] = [:]
                var beforeStringSettings: [String: Bool] = [:]
                
                for prayer in Prayer.allCases {
                    stringSettings[prayer.displayName] = notificationSettings[prayer] ?? false
                    beforeStringSettings[prayer.displayName] = beforeNotificationSettings[prayer] ?? false
                }
                
                settings.notificationSettings = stringSettings
                settings.beforeNotificationSettings = beforeStringSettings
                settings.beforeMinutes = beforeMinutes
                
                if settings.id == UUID() {
                    try repository.saveUserSettings(settings)
                } else {
                    try repository.updateUserSettings(settings)
                }
            } catch {
                print("Failed to save notification settings: \(error)")
            }
        }
    }
    
    private func registerBackgroundTasks() {
        BGTaskScheduler.shared.register(
            forTaskWithIdentifier: AppConstants.BackgroundTasks.notificationRefresh,
            using: nil
        ) { task in
            self.handleBackgroundNotificationRefresh(task as! BGAppRefreshTask)
        }
    }
    
    // MARK: - Public Methods
    func requestNotificationPermission() async -> Bool {
        do {
            let granted = try await notificationCenter.requestAuthorization(options: [.alert, .sound, .badge])
            self.isAuthorized = granted
            
            if granted {
                await scheduleBackgroundRefresh()
            }
            
            return granted
        } catch {
            print("Failed to request notification permission: \(error)")
            self.isAuthorized = false
            return false
        }
    }
    
    func scheduleNotifications(for prayerTimes: [PrayerTimes]) async throws {
        guard isAuthorized else {
            throw NotificationError.permissionDenied
        }
        
        // Cancel existing notifications
        cancelAllNotifications()
        
        var scheduledCount = 0
        let maxNotifications = AppConstants.Notifications.maxScheduledNotifications
        
        for prayerTime in prayerTimes {
            if scheduledCount >= maxNotifications { break }
            
            for prayer in Prayer.allCases {
                if scheduledCount >= maxNotifications { break }
                
                let prayerDate = getPrayerTime(for: prayer, from: prayerTime)
                
                // Schedule main notification
                if notificationSettings[prayer] == true {
                    try await scheduleNotification(
                        for: prayer,
                        at: prayerDate,
                        isBefore: false
                    )
                    scheduledCount += 1
                }
                
                // Schedule before notification
                if beforeNotificationSettings[prayer] == true {
                    let beforeDate = prayerDate.addingTimeInterval(-TimeInterval(beforeMinutes * 60))
                    if beforeDate > Date() {
                        try await scheduleNotification(
                            for: prayer,
                            at: beforeDate,
                            isBefore: true
                        )
                        scheduledCount += 1
                    }
                }
            }
        }
        
        // Schedule background refresh
        await scheduleBackgroundRefresh()
        
        print("Scheduled \(scheduledCount) notifications")
    }
    
    func cancelAllNotifications() {
        notificationCenter.removeAllPendingNotificationRequests()
    }
    
    func updateNotificationSettings(for prayer: Prayer, enabled: Bool, isBefore: Bool = false) {
        if isBefore {
            beforeNotificationSettings[prayer] = enabled
        } else {
            notificationSettings[prayer] = enabled
        }
    }
    
    // MARK: - Private Methods
    private func checkAuthorizationStatus() async {
        let settings = await notificationCenter.notificationSettings()
        self.isAuthorized = settings.authorizationStatus == .authorized
    }
    
    private func scheduleNotification(
        for prayer: Prayer,
        at date: Date,
        isBefore: Bool
    ) async throws {
        let content = UNMutableNotificationContent()
        
        if isBefore {
            content.title = "Prayer Time Reminder"
            content.body = "\(prayer.displayName) prayer is in \(beforeMinutes) minutes"
            content.categoryIdentifier = "BEFORE_PRAYER_REMINDER"
        } else {
            content.title = "Prayer Time"
            content.body = "It's time for \(prayer.displayName) prayer"
            content.categoryIdentifier = "PRAYER_TIME"
        }
        
        content.sound = .default
        content.badge = 1
        
        // Add custom data
        content.userInfo = [
            "prayer": prayer.displayName,
            "isBefore": isBefore,
            "scheduledTime": date.timeIntervalSince1970
        ]
        
        // Create trigger
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month, .day, .hour, .minute], from: date)
        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: false)
        
        // Create request
        let identifier = generateNotificationIdentifier(prayer: prayer, date: date, isBefore: isBefore)
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)
        
        // Schedule notification
        try await notificationCenter.add(request)
        
        // Save to history
        await saveNotificationHistory(prayer: prayer, scheduledTime: date, isBefore: isBefore)
    }
    
    private func generateNotificationIdentifier(prayer: Prayer, date: Date, isBefore: Bool) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd-HH-mm"
        let dateString = formatter.string(from: date)
        let prefix = isBefore ? "before" : "prayer"
        return "\(prefix)_\(prayer.displayName.lowercased())_\(dateString)"
    }
    
    private func getPrayerTime(for prayer: Prayer, from prayerTimes: PrayerTimes) -> Date {
        switch prayer {
        case .fajr: return prayerTimes.fajr
        case .sunrise: return prayerTimes.sunrise
        case .dhuhr: return prayerTimes.dhuhr
        case .asr: return prayerTimes.asr
        case .maghrib: return prayerTimes.maghrib
        case .isha: return prayerTimes.isha
        }
    }
    
    private func saveNotificationHistory(prayer: Prayer, scheduledTime: Date, isBefore: Bool) async {
        do {
            // We need a location ID, but we don't have access to location service here
            // In a real implementation, this would be passed in or retrieved from a coordinator
            let locationId = UUID() // Placeholder
            
            let history = NotificationHistory(
                prayer: prayer,
                scheduledTime: scheduledTime,
                isBefore: isBefore,
                beforeMinutes: isBefore ? beforeMinutes : nil,
                locationId: locationId
            )
            
            try repository.saveNotificationHistory(history)
        } catch {
            print("Failed to save notification history: \(error)")
        }
    }
    
    private func scheduleBackgroundRefresh() async {
        let request = BGAppRefreshTaskRequest(identifier: AppConstants.BackgroundTasks.notificationRefresh)
        request.earliestBeginDate = Date(timeIntervalSinceNow: 24 * 60 * 60) // 24 hours from now
        
        do {
            try BGTaskScheduler.shared.submit(request)
            print("Background refresh scheduled")
        } catch {
            print("Failed to schedule background refresh: \(error)")
        }
    }
    
    private func handleBackgroundNotificationRefresh(_ task: BGAppRefreshTask) {
        // Schedule the next background refresh
        Task {
            await scheduleBackgroundRefresh()
        }
        
        task.expirationHandler = {
            task.setTaskCompleted(success: false)
        }
        
        // Perform the background work
        Task {
            do {
                // This would typically involve:
                // 1. Getting current location
                // 2. Calculating new prayer times
                // 3. Scheduling new notifications
                // For now, we'll just mark the task as completed
                
                task.setTaskCompleted(success: true)
            }
        }
    }
}

// MARK: - UNUserNotificationCenterDelegate
extension NotificationService: UNUserNotificationCenterDelegate {
    
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // Show notification even when app is in foreground
        completionHandler([.alert, .sound, .badge])
    }
    
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        let userInfo = response.notification.request.content.userInfo
        
        // Handle notification response
        if let prayerName = userInfo["prayer"] as? String,
           let isBefore = userInfo["isBefore"] as? Bool {
            
            print("User responded to \(isBefore ? "before" : "") \(prayerName) notification")
            
            // Mark notification as delivered in history
            Task {
                // Implementation would mark the notification as delivered
            }
        }
        
        completionHandler()
    }
}
