//
//  PrayerTimeService.swift
//  PrayerEase
//
//  Created by <PERSON><PERSON><PERSON> on 12/24/24.
//

import Foundation
import CoreLocation
import Adhan
import Combine

// MARK: - Enhanced Prayer Time Service
@MainActor
final class PrayerTimeService: PrayerTimeServiceProtocol {
    
    // MARK: - Published Properties
    @Published private(set) var currentPrayerTimes: PrayerTimes?
    @Published var calculationMethod: CalculationMethod = .turkey {
        didSet { handleSettingsChange() }
    }
    @Published var madhab: Madhab = .shafi {
        didSet { handleSettingsChange() }
    }
    @Published private(set) var nextPrayer: Prayer?
    @Published private(set) var timeUntilNextPrayer: TimeInterval?
    
    // MARK: - Private Properties
    private let repository: SwiftDataRepository
    private var prayerTimesCache: [String: PrayerTimes] = [:]
    private var updateTimer: Timer?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init(repository: SwiftDataRepository) {
        self.repository = repository
        setupTimer()
        loadSettings()
    }
    
    deinit {
        updateTimer?.invalidate()
    }
    
    // MARK: - Setup
    private func setupTimer() {
        // Update next prayer and time remaining every minute
        updateTimer = Timer.scheduledTimer(withTimeInterval: 60, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateNextPrayerInfo()
            }
        }
    }
    
    private func loadSettings() {
        Task {
            do {
                if let settings = try repository.fetchUserSettings() {
                    self.calculationMethod = settings.calculationMethod
                    self.madhab = settings.madhab
                }
            } catch {
                print("Failed to load prayer time settings: \(error)")
            }
        }
    }
    
    private func handleSettingsChange() {
        Task {
            await saveSettings()
            // Clear cache when settings change
            prayerTimesCache.removeAll()
            
            // Recalculate current prayer times if we have a location
            if let currentTimes = currentPrayerTimes {
                // We need the location to recalculate, but we don't store it here
                // This would typically be handled by a coordinator or the main app
                updateNextPrayerInfo()
            }
        }
    }
    
    private func saveSettings() async {
        do {
            var settings = try repository.fetchUserSettings() ?? UserSettings()
            settings.calculationMethod = calculationMethod
            settings.madhab = madhab
            
            if settings.id == UUID() { // New settings
                try repository.saveUserSettings(settings)
            } else {
                try repository.updateUserSettings(settings)
            }
        } catch {
            print("Failed to save prayer time settings: \(error)")
        }
    }
    
    // MARK: - Public Methods
    func calculatePrayerTimes(for date: Date, location: CLLocation) async throws -> PrayerTimes {
        let cacheKey = generateCacheKey(date: date, location: location)
        
        // Check memory cache first
        if let cachedTimes = prayerTimesCache[cacheKey] {
            return cachedTimes
        }
        
        // Check persistent cache
        if let cachedData = try await fetchFromPersistentCache(date: date, location: location) {
            prayerTimesCache[cacheKey] = cachedData
            return cachedData
        }
        
        // Calculate new prayer times
        let prayerTimes = try await performCalculation(for: date, location: location)
        
        // Cache the results
        prayerTimesCache[cacheKey] = prayerTimes
        await saveToPersistentCache(prayerTimes, date: date, location: location)
        
        // Update current prayer times if this is for today
        if Calendar.current.isDate(date, inSameDayAs: Date()) {
            self.currentPrayerTimes = prayerTimes
            updateNextPrayerInfo()
        }
        
        return prayerTimes
    }
    
    func getMonthlyPrayerTimes(for date: Date, location: CLLocation) async throws -> [PrayerTimes] {
        let calendar = Calendar.current
        let startOfMonth = calendar.dateInterval(of: .month, for: date)?.start ?? date
        let daysInMonth = calendar.range(of: .day, in: .month, for: date)?.count ?? 30
        
        var monthlyTimes: [PrayerTimes] = []
        
        for dayOffset in 0..<daysInMonth {
            guard let currentDate = calendar.date(byAdding: .day, value: dayOffset, to: startOfMonth) else {
                continue
            }
            
            do {
                let prayerTimes = try await calculatePrayerTimes(for: currentDate, location: location)
                monthlyTimes.append(prayerTimes)
            } catch {
                print("Failed to calculate prayer times for \(currentDate): \(error)")
                // Continue with other days even if one fails
            }
        }
        
        return monthlyTimes
    }
    
    func getCurrentPrayer() -> Prayer? {
        guard let prayerTimes = currentPrayerTimes else { return nil }
        return prayerTimes.currentPrayer()
    }
    
    func getNextPrayer() -> Prayer? {
        guard let prayerTimes = currentPrayerTimes else { return nil }
        return prayerTimes.nextPrayer()
    }
    
    // MARK: - Private Methods
    private func performCalculation(for date: Date, location: CLLocation) async throws -> PrayerTimes {
        return try await withCheckedThrowingContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                do {
                    let coordinates = Coordinates(
                        latitude: location.coordinate.latitude,
                        longitude: location.coordinate.longitude
                    )
                    
                    var params = self.calculationMethod.params
                    params.madhab = self.madhab
                    
                    let dateComponents = Calendar.current.dateComponents([.year, .month, .day], from: date)
                    
                    guard let prayerTimes = PrayerTimes(
                        coordinates: coordinates,
                        date: dateComponents,
                        calculationParameters: params
                    ) else {
                        continuation.resume(throwing: PrayerTimeError.calculationFailed)
                        return
                    }
                    
                    continuation.resume(returning: prayerTimes)
                } catch {
                    continuation.resume(throwing: PrayerTimeError.unknown(error))
                }
            }
        }
    }
    
    private func fetchFromPersistentCache(date: Date, location: CLLocation) async throws -> PrayerTimes? {
        // For now, we'll use a simplified approach since PrayerTimes doesn't conform to Codable
        // In a real implementation, you might need to create a custom PrayerTimes wrapper
        return nil
    }
    
    private func saveToPersistentCache(_ prayerTimes: PrayerTimes, date: Date, location: CLLocation) async {
        // Implementation would save to SwiftData cache
        // This is simplified for now
    }
    
    private func generateCacheKey(date: Date, location: CLLocation) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)
        
        let locationString = String(format: "%.4f,%.4f", location.coordinate.latitude, location.coordinate.longitude)
        
        return "\(dateString)_\(locationString)_\(calculationMethod.rawValue)_\(madhab.rawValue)"
    }
    
    private func updateNextPrayerInfo() {
        guard let prayerTimes = currentPrayerTimes else {
            nextPrayer = nil
            timeUntilNextPrayer = nil
            return
        }
        
        let now = Date()
        let prayers: [(Prayer, Date)] = [
            (.fajr, prayerTimes.fajr),
            (.sunrise, prayerTimes.sunrise),
            (.dhuhr, prayerTimes.dhuhr),
            (.asr, prayerTimes.asr),
            (.maghrib, prayerTimes.maghrib),
            (.isha, prayerTimes.isha)
        ]
        
        // Find the next prayer
        for (prayer, time) in prayers {
            if time > now {
                nextPrayer = prayer
                timeUntilNextPrayer = time.timeIntervalSince(now)
                return
            }
        }
        
        // If no prayer is found for today, the next prayer is Fajr tomorrow
        nextPrayer = .fajr
        
        // Calculate tomorrow's Fajr time
        if let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: now) {
            Task {
                // This would need access to location service
                // For now, we'll set a placeholder
                timeUntilNextPrayer = nil
            }
        }
    }
    
    // MARK: - Utility Methods
    func formatPrayerTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
    
    func getTimeRemaining(until prayer: Prayer) -> String? {
        guard let timeInterval = timeUntilNextPrayer else { return nil }
        
        let hours = Int(timeInterval) / 3600
        let minutes = Int(timeInterval) % 3600 / 60
        
        if hours > 0 {
            return String(format: "%dh %dm", hours, minutes)
        } else {
            return String(format: "%dm", minutes)
        }
    }
    
    func getPrayerTime(for prayer: Prayer, from prayerTimes: PrayerTimes) -> Date {
        switch prayer {
        case .fajr: return prayerTimes.fajr
        case .sunrise: return prayerTimes.sunrise
        case .dhuhr: return prayerTimes.dhuhr
        case .asr: return prayerTimes.asr
        case .maghrib: return prayerTimes.maghrib
        case .isha: return prayerTimes.isha
        }
    }
    
    // MARK: - Cache Management
    func clearCache() {
        prayerTimesCache.removeAll()
        
        Task {
            do {
                try repository.cleanExpiredPrayerTimeCache()
            } catch {
                print("Failed to clean prayer time cache: \(error)")
            }
        }
    }
    
    func preloadPrayerTimes(for dates: [Date], location: CLLocation) async {
        for date in dates {
            do {
                _ = try await calculatePrayerTimes(for: date, location: location)
            } catch {
                print("Failed to preload prayer times for \(date): \(error)")
            }
        }
    }
}
