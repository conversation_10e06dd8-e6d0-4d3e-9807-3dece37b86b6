//
//  AppCoordinator.swift
//  PrayerEase
//
//  Created by <PERSON><PERSON><PERSON> on 12/24/24.
//

import Foundation
import SwiftUI
import Combine
import CoreLocation
import Adhan

// MARK: - App Coordinator
@MainActor
final class AppCoordinator: ObservableObject {
    
    // MARK: - Services
    let locationService: LocationService
    let prayerTimeService: PrayerTimeService
    let notificationService: NotificationService
    let configurationService: ConfigurationService
    let repository: SwiftDataRepository
    
    // MARK: - Published State
    @Published var isInitialized = false
    @Published var hasLocationPermission = false
    @Published var hasNotificationPermission = false
    @Published var currentError: AppError?
    @Published var isLoading = false
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    private var initializationTask: Task<Void, Never>?
    
    // MARK: - Initialization
    init() {
        do {
            // Initialize repository first
            self.repository = try SwiftDataRepository()
            
            // Initialize services with dependency injection
            self.configurationService = ConfigurationService.shared
            self.locationService = LocationService(repository: repository)
            self.prayerTimeService = PrayerTimeService(repository: repository)
            self.notificationService = NotificationService(repository: repository)
            
            setupBindings()
            
        } catch {
            // Fallback initialization if repository fails
            fatalError("Failed to initialize core services: \(error)")
        }
    }
    
    deinit {
        initializationTask?.cancel()
    }
    
    // MARK: - Setup
    private func setupBindings() {
        // Bind location service to prayer time service
        locationService.$currentLocation
            .compactMap { $0 }
            .sink { [weak self] location in
                Task { @MainActor in
                    await self?.handleLocationUpdate(location)
                }
            }
            .store(in: &cancellables)
        
        // Bind location authorization status
        locationService.$isLocationAuthorized
            .assign(to: \.hasLocationPermission, on: self)
            .store(in: &cancellables)
        
        // Bind notification authorization status
        notificationService.$isAuthorized
            .assign(to: \.hasNotificationPermission, on: self)
            .store(in: &cancellables)
        
        // Bind errors
        locationService.$error
            .compactMap { $0 }
            .map { AppError.location($0) }
            .assign(to: \.currentError, on: self)
            .store(in: &cancellables)
    }
    
    // MARK: - App Lifecycle
    func initializeApp() async {
        guard !isInitialized else { return }
        
        isLoading = true
        
        do {
            // Validate configuration
            let configIssues = configurationService.validateConfiguration()
            if !configIssues.isEmpty {
                print("Configuration issues: \(configIssues)")
            }
            
            // Request permissions
            await requestPermissions()
            
            // Initialize location services
            if hasLocationPermission {
                await locationService.startLocationUpdates()
            }
            
            // Perform maintenance tasks
            await performMaintenanceTasks()
            
            isInitialized = true
            
        } catch {
            currentError = AppError.initialization(error)
        }
        
        isLoading = false
    }
    
    private func requestPermissions() async {
        // Request location permission
        await locationService.requestLocationPermission()
        
        // Request notification permission
        _ = await notificationService.requestNotificationPermission()
    }
    
    private func performMaintenanceTasks() async {
        do {
            // Clean up old data
            try repository.performMaintenance()
            
            // Update prayer times if needed
            if let location = locationService.currentLocation {
                await updatePrayerTimesIfNeeded(for: location)
            }
            
        } catch {
            print("Maintenance tasks failed: \(error)")
        }
    }
    
    // MARK: - Location Handling
    private func handleLocationUpdate(_ location: CLLocation) async {
        await updatePrayerTimesIfNeeded(for: location)
        await scheduleNotificationsIfNeeded()
    }
    
    private func updatePrayerTimesIfNeeded(for location: CLLocation) async {
        do {
            let today = Date()
            let prayerTimes = try await prayerTimeService.calculatePrayerTimes(for: today, location: location)
            
            // Preload next few days
            let nextDays = (1...7).compactMap { Calendar.current.date(byAdding: .day, value: $0, to: today) }
            await prayerTimeService.preloadPrayerTimes(for: nextDays, location: location)
            
        } catch {
            currentError = AppError.prayerTime(error as? PrayerTimeError ?? .unknown(error))
        }
    }
    
    private func scheduleNotificationsIfNeeded() async {
        guard hasNotificationPermission,
              let location = locationService.currentLocation else { return }
        
        do {
            // Get prayer times for the next week
            let dates = (0..<7).compactMap { Calendar.current.date(byAdding: .day, value: $0, to: Date()) }
            let prayerTimes = try await prayerTimeService.getMonthlyPrayerTimes(for: Date(), location: location)
            
            // Schedule notifications
            try await notificationService.scheduleNotifications(for: prayerTimes)
            
        } catch {
            print("Failed to schedule notifications: \(error)")
        }
    }
    
    // MARK: - Public Methods
    func refreshData() async {
        isLoading = true
        currentError = nil
        
        do {
            // Refresh location
            await locationService.startLocationUpdates()
            
            // Clear prayer time cache
            prayerTimeService.clearCache()
            
            // Update prayer times
            if let location = locationService.currentLocation {
                await updatePrayerTimesIfNeeded(for: location)
            }
            
        } catch {
            currentError = AppError.unknown(error)
        }
        
        isLoading = false
    }
    
    func handleAppDidBecomeActive() async {
        // Refresh data when app becomes active
        if isInitialized {
            await refreshData()
        }
    }
    
    func handleAppWillResignActive() {
        // Perform any cleanup when app goes to background
        locationService.stopLocationUpdates()
    }
    
    // MARK: - Error Handling
    func clearError() {
        currentError = nil
    }
    
    func retryLastOperation() async {
        guard let error = currentError else { return }
        
        clearError()
        
        switch error {
        case .location:
            await locationService.startLocationUpdates()
        case .prayerTime:
            if let location = locationService.currentLocation {
                await updatePrayerTimesIfNeeded(for: location)
            }
        case .notification:
            await scheduleNotificationsIfNeeded()
        case .initialization, .unknown:
            await initializeApp()
        }
    }
}

// MARK: - App Error Types
enum AppError: LocalizedError, Equatable {
    case location(LocationError)
    case prayerTime(PrayerTimeError)
    case notification(NotificationError)
    case initialization(Error)
    case unknown(Error)
    
    var errorDescription: String? {
        switch self {
        case .location(let error):
            return error.errorDescription
        case .prayerTime(let error):
            return error.errorDescription
        case .notification(let error):
            return error.errorDescription
        case .initialization(let error):
            return "Failed to initialize app: \(error.localizedDescription)"
        case .unknown(let error):
            return "An unexpected error occurred: \(error.localizedDescription)"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .location:
            return "Please check your location settings and try again."
        case .prayerTime:
            return "Please check your internet connection and try again."
        case .notification:
            return "Please check your notification settings and try again."
        case .initialization:
            return "Please restart the app."
        case .unknown:
            return "Please try again or restart the app."
        }
    }
    
    static func == (lhs: AppError, rhs: AppError) -> Bool {
        switch (lhs, rhs) {
        case (.location, .location),
             (.prayerTime, .prayerTime),
             (.notification, .notification),
             (.initialization, .initialization),
             (.unknown, .unknown):
            return true
        default:
            return false
        }
    }
}

// MARK: - App State
extension AppCoordinator {
    
    var isReady: Bool {
        isInitialized && hasLocationPermission
    }
    
    var needsLocationPermission: Bool {
        !hasLocationPermission && isInitialized
    }
    
    var needsNotificationPermission: Bool {
        !hasNotificationPermission && isInitialized
    }
    
    var canShowPrayerTimes: Bool {
        isReady && locationService.currentLocation != nil
    }
    
    var canShowQibla: Bool {
        isReady && locationService.currentLocation != nil
    }
    
    var canUseChat: Bool {
        configurationService.isFeatureEnabled("chat") && !configurationService.apiKeys.openAI.isEmpty
    }
}
