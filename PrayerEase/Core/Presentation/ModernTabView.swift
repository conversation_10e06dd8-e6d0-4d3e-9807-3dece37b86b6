//
//  ModernTabView.swift
//  PrayerEase
//
//  Created by <PERSON><PERSON><PERSON> on 12/24/24.
//

import SwiftUI

// MARK: - Modern Tab View
struct ModernTabView: View {
    @EnvironmentObject private var appCoordinator: AppCoordinator
    @State private var selectedTab: AppTab = .prayerTimes
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // Prayer Times Tab
            NavigationStack {
                PrayerTimesView()
            }
            .tabItem {
                Label(AppTab.prayerTimes.title, systemImage: AppTab.prayerTimes.systemImage)
            }
            .tag(AppTab.prayerTimes)
            
            // Qibla Tab
            NavigationStack {
                QiblaView()
            }
            .tabItem {
                Label(AppTab.qibla.title, systemImage: AppTab.qibla.systemImage)
            }
            .tag(AppTab.qibla)
            
            // Chat Tab (if enabled)
            if appCoordinator.canUseChat {
                NavigationStack {
                    ChatView()
                        .environmentObject(appCoordinator.configurationService)
                }
                .tabItem {
                    Label(AppTab.chat.title, systemImage: AppTab.chat.systemImage)
                }
                .tag(AppTab.chat)
            }

            // Calendar Tab
            NavigationStack {
                CalendarView()
                    .environmentObject(appCoordinator.prayerTimeService)
                    .environmentObject(appCoordinator.locationService)
            }
            .tabItem {
                Label(AppTab.calendar.title, systemImage: AppTab.calendar.systemImage)
            }
            .tag(AppTab.calendar)

            // Settings Tab
            NavigationStack {
                SettingsView()
                    .environmentObject(appCoordinator.prayerTimeService)
                    .environmentObject(appCoordinator.notificationService)
                    .environmentObject(appCoordinator.configurationService)
                    .environmentObject(appCoordinator)
            }
            .tabItem {
                Label(AppTab.settings.title, systemImage: AppTab.settings.systemImage)
            }
            .tag(AppTab.settings)
        }
        .tint(.accentColor)
        .onAppear {
            setupTabBarAppearance()
        }
    }
    
    private func setupTabBarAppearance() {
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor.systemBackground
        
        // Configure selected item appearance
        appearance.stackedLayoutAppearance.selected.iconColor = UIColor.systemBlue
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = [
            .foregroundColor: UIColor.systemBlue
        ]
        
        // Configure normal item appearance
        appearance.stackedLayoutAppearance.normal.iconColor = UIColor.systemGray
        appearance.stackedLayoutAppearance.normal.titleTextAttributes = [
            .foregroundColor: UIColor.systemGray
        ]
        
        UITabBar.appearance().standardAppearance = appearance
        UITabBar.appearance().scrollEdgeAppearance = appearance
    }
}

// MARK: - App Tab Enum
enum AppTab: String, CaseIterable {
    case prayerTimes = "prayer_times"
    case qibla = "qibla"
    case chat = "chat"
    case calendar = "calendar"
    case settings = "settings"
    
    var title: String {
        switch self {
        case .prayerTimes:
            return "Prayer Times"
        case .qibla:
            return "Qibla"
        case .chat:
            return "Chat"
        case .calendar:
            return "Calendar"
        case .settings:
            return "Settings"
        }
    }
    
    var systemImage: String {
        switch self {
        case .prayerTimes:
            return "clock.fill"
        case .qibla:
            return "safari.fill"
        case .chat:
            return "apple.intelligence"
        case .calendar:
            return "calendar"
        case .settings:
            return "gear"
        }
    }
    
    var accessibilityLabel: String {
        switch self {
        case .prayerTimes:
            return "Prayer Times Tab"
        case .qibla:
            return "Qibla Direction Tab"
        case .chat:
            return "AI Chat Tab"
        case .calendar:
            return "Prayer Calendar Tab"
        case .settings:
            return "Settings Tab"
        }
    }
}

// MARK: - Placeholder Views (to be implemented)
struct PrayerTimesView: View {
    @EnvironmentObject private var appCoordinator: AppCoordinator
    @EnvironmentObject private var locationService: LocationService
    @EnvironmentObject private var prayerTimeService: PrayerTimeService
    
    var body: some View {
        Group {
            if appCoordinator.canShowPrayerTimes {
                ModernPrayerTimesContent()
            } else if appCoordinator.needsLocationPermission {
                LocationPermissionView()
            } else {
                LoadingPrayerTimesView()
            }
        }
        .navigationTitle("Prayer Times")
        .navigationBarTitleDisplayMode(.large)
    }
}

struct ModernPrayerTimesContent: View {
    @EnvironmentObject private var prayerTimeService: PrayerTimeService
    @EnvironmentObject private var locationService: LocationService
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Current Prayer Status Card
                if let currentPrayerTimes = prayerTimeService.currentPrayerTimes {
                    CurrentPrayerCard(prayerTimes: currentPrayerTimes)
                        .padding(.horizontal)
                }
                
                // Prayer Times List
                if let prayerTimes = prayerTimeService.currentPrayerTimes {
                    PrayerTimesListCard(prayerTimes: prayerTimes)
                        .padding(.horizontal)
                }
                
                // Location Info
                LocationInfoCard()
                    .padding(.horizontal)
            }
            .padding(.vertical)
        }
        .refreshable {
            await refreshPrayerTimes()
        }
    }
    
    private func refreshPrayerTimes() async {
        guard let location = locationService.currentLocation else { return }
        
        do {
            _ = try await prayerTimeService.calculatePrayerTimes(for: Date(), location: location)
        } catch {
            print("Failed to refresh prayer times: \(error)")
        }
    }
}

struct CurrentPrayerCard: View {
    let prayerTimes: PrayerTimes
    @EnvironmentObject private var prayerTimeService: PrayerTimeService
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Current Prayer")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    if let currentPrayer = prayerTimeService.getCurrentPrayer() {
                        Text(currentPrayer.displayName)
                            .font(.title2)
                            .fontWeight(.semibold)
                    } else {
                        Text("Between Prayers")
                            .font(.title2)
                            .fontWeight(.semibold)
                    }
                }
                
                Spacer()
                
                if let nextPrayer = prayerTimeService.getNextPrayer(),
                   let timeRemaining = prayerTimeService.getTimeRemaining(until: nextPrayer) {
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("Next: \(nextPrayer.displayName)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(timeRemaining)
                            .font(.headline)
                            .foregroundColor(.accentColor)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct PrayerTimesListCard: View {
    let prayerTimes: PrayerTimes
    @EnvironmentObject private var prayerTimeService: PrayerTimeService
    
    var body: some View {
        VStack(spacing: 0) {
            ForEach(Prayer.allCases, id: \.self) { prayer in
                let prayerTime = prayerTimeService.getPrayerTime(for: prayer, from: prayerTimes)
                let isCurrentPrayer = prayerTimeService.getCurrentPrayer() == prayer
                
                ModernPrayerTimeRow(
                    prayer: prayer,
                    time: prayerTime,
                    isCurrentPrayer: isCurrentPrayer
                )
                
                if prayer != Prayer.allCases.last {
                    Divider()
                        .padding(.leading, 60)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct ModernPrayerTimeRow: View {
    let prayer: Prayer
    let time: Date
    let isCurrentPrayer: Bool
    @EnvironmentObject private var notificationService: NotificationService
    
    var body: some View {
        HStack(spacing: 16) {
            // Prayer Icon
            Image(systemName: prayer.systemImageName)
                .font(.title2)
                .foregroundColor(isCurrentPrayer ? .accentColor : .secondary)
                .frame(width: 30)
            
            // Prayer Name
            Text(prayer.displayName)
                .font(.body)
                .fontWeight(isCurrentPrayer ? .semibold : .regular)
                .foregroundColor(isCurrentPrayer ? .accentColor : .primary)
            
            Spacer()
            
            // Notification Toggle
            Button(action: {
                toggleNotification()
            }) {
                Image(systemName: isNotificationEnabled ? "bell.fill" : "bell.slash")
                    .font(.body)
                    .foregroundColor(isNotificationEnabled ? .accentColor : .secondary)
            }
            .accessibilityLabel("Toggle notifications for \(prayer.displayName)")
            
            // Prayer Time
            Text(time, style: .time)
                .font(.body)
                .fontWeight(isCurrentPrayer ? .semibold : .regular)
                .foregroundColor(isCurrentPrayer ? .accentColor : .primary)
                .frame(width: 80, alignment: .trailing)
        }
        .padding(.vertical, 8)
    }
    
    private var isNotificationEnabled: Bool {
        notificationService.notificationSettings[prayer] ?? false
    }
    
    private func toggleNotification() {
        notificationService.updateNotificationSettings(
            for: prayer,
            enabled: !isNotificationEnabled
        )
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
}

struct LocationInfoCard: View {
    @EnvironmentObject private var locationService: LocationService
    
    var body: some View {
        HStack {
            Image(systemName: locationService.isLocationAuthorized ? "location.fill" : "location.slash")
                .foregroundColor(locationService.isLocationAuthorized ? .green : .red)
            
            Text(locationService.locationName)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
            
            if !locationService.isLocationAuthorized {
                Button("Enable Location") {
                    Task {
                        await locationService.requestLocationPermission()
                    }
                }
                .font(.caption)
                .buttonStyle(.borderedProminent)
            }
        }
        .padding()
        .background(Color(.secondarySystemBackground))
        .cornerRadius(8)
    }
}

struct LocationPermissionView: View {
    @EnvironmentObject private var locationService: LocationService
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "location.slash")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text("Location Access Required")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("PrayerEase needs your location to calculate accurate prayer times for your area.")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            
            Button("Enable Location Services") {
                Task {
                    await locationService.requestLocationPermission()
                }
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
        }
        .padding()
    }
}

struct LoadingPrayerTimesView: View {
    var body: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("Loading prayer times...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }
}

struct ModernQiblaContent: View {
    @EnvironmentObject private var locationService: LocationService
    @State private var compassSize: CGFloat = 250

    var qiblaDirection: Double {
        locationService.calculateQiblaDirection() ?? 0
    }

    var isPointingToQibla: Bool {
        let difference = abs(locationService.heading - qiblaDirection)
        return difference <= AppConstants.Location.qiblaAccuracyThreshold
    }

    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 30) {
                // Compass View
                ZStack {
                    // Compass Background
                    Circle()
                        .stroke(Color.secondary.opacity(0.3), lineWidth: 2)
                        .frame(width: compassSize, height: compassSize)

                    // Compass Markings
                    ForEach(0..<360, id: \.self) { degree in
                        if degree % 30 == 0 {
                            Rectangle()
                                .fill(Color.secondary)
                                .frame(width: 2, height: 20)
                                .offset(y: -compassSize/2 + 10)
                                .rotationEffect(.degrees(Double(degree)))
                        }
                    }

                    // Cardinal Directions
                    VStack {
                        Text("N")
                            .font(.headline)
                            .fontWeight(.bold)
                            .offset(y: -compassSize/2 - 20)

                        Spacer()

                        Text("S")
                            .font(.headline)
                            .fontWeight(.bold)
                            .offset(y: compassSize/2 + 20)
                    }
                    .frame(height: compassSize)

                    HStack {
                        Text("W")
                            .font(.headline)
                            .fontWeight(.bold)
                            .offset(x: -compassSize/2 - 20)

                        Spacer()

                        Text("E")
                            .font(.headline)
                            .fontWeight(.bold)
                            .offset(x: compassSize/2 + 20)
                    }
                    .frame(width: compassSize)

                    // Qibla Direction Arrow
                    Image(systemName: "arrow.up")
                        .font(.system(size: 40, weight: .bold))
                        .foregroundColor(.green)
                        .rotationEffect(.degrees(qiblaDirection))

                    // Device Heading Arrow
                    Image(systemName: "arrow.up")
                        .font(.system(size: 30, weight: .medium))
                        .foregroundColor(isPointingToQibla ? .accentColor : .red)
                        .rotationEffect(.degrees(locationService.heading))
                        .offset(y: -20)
                }
                .frame(width: compassSize, height: compassSize)

                // Information Cards
                VStack(spacing: 16) {
                    QiblaInfoCard(
                        title: "Qibla Direction",
                        value: "\(Int(qiblaDirection))°",
                        icon: "safari.fill",
                        color: .green
                    )

                    QiblaInfoCard(
                        title: "Device Heading",
                        value: "\(Int(locationService.heading))°",
                        icon: "compass.drawing",
                        color: isPointingToQibla ? .accentColor : .red
                    )

                    QiblaInfoCard(
                        title: "Accuracy",
                        value: String(format: "±%.1f°", locationService.headingAccuracy),
                        icon: "target",
                        color: .secondary
                    )
                }

                // Status Message
                VStack(spacing: 8) {
                    Text(isPointingToQibla ? "Pointing to Qibla" : "Align to the Qibla")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(isPointingToQibla ? .green : .primary)

                    if !isPointingToQibla {
                        let difference = abs(locationService.heading - qiblaDirection)
                        let direction = locationService.heading < qiblaDirection ? "right" : "left"
                        Text("Turn \(String(format: "%.0f", difference))° to the \(direction)")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()
            }
            .padding()
            .onAppear {
                compassSize = min(geometry.size.width * 0.7, 300)
            }
        }
        .onChange(of: isPointingToQibla) { _, newValue in
            if newValue {
                let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
                impactFeedback.impactOccurred()
            }
        }
    }
}

struct QiblaInfoCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        HStack {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 30)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text(value)
                    .font(.headline)
                    .fontWeight(.semibold)
            }

            Spacer()
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct LoadingQiblaView: View {
    var body: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)

            Text("Initializing compass...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }
}

#Preview {
    ModernTabView()
        .environmentObject(AppCoordinator())
}
