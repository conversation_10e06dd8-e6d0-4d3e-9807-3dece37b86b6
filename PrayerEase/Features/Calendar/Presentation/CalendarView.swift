//
//  CalendarView.swift
//  PrayerEase
//
//  Created by <PERSON><PERSON><PERSON> on 12/24/24.
//

import SwiftUI
import Adhan

// MARK: - Modern Calendar View
struct CalendarView: View {
    @EnvironmentObject private var prayerTimeService: PrayerTimeService
    @EnvironmentObject private var locationService: LocationService
    @State private var selectedDate = Date()
    @State private var monthlyPrayerTimes: [PrayerTimes] = []
    @State private var isLoading = false
    @State private var currentMonth = Date()
    
    var body: some View {
        VStack(spacing: 0) {
            // Month Navigation
            MonthNavigationView(currentMonth: $currentMonth) {
                await loadMonthlyPrayerTimes()
            }
            
            // Calendar Grid
            if isLoading {
                Spacer()
                ProgressView("Loading prayer times...")
                    .scaleEffect(1.2)
                Spacer()
            } else {
                ScrollView {
                    LazyVStack(spacing: 16) {
                        // Calendar Header
                        CalendarHeaderView()
                        
                        // Prayer Times List
                        ForEach(Array(monthlyPrayerTimes.enumerated()), id: \.offset) { index, prayerTimes in
                            let dayNumber = index + 1
                            let isToday = Calendar.current.isDate(
                                Calendar.current.date(byAdding: .day, value: index, to: startOfMonth) ?? Date(),
                                inSameDayAs: Date()
                            )
                            
                            CalendarDayRow(
                                dayNumber: dayNumber,
                                prayerTimes: prayerTimes,
                                isToday: isToday
                            )
                        }
                    }
                    .padding()
                }
            }
        }
        .navigationTitle("Prayer Calendar")
        .navigationBarTitleDisplayMode(.large)
        .task {
            await loadMonthlyPrayerTimes()
        }
        .onChange(of: currentMonth) { _, _ in
            Task {
                await loadMonthlyPrayerTimes()
            }
        }
    }
    
    private var startOfMonth: Date {
        Calendar.current.dateInterval(of: .month, for: currentMonth)?.start ?? currentMonth
    }
    
    private func loadMonthlyPrayerTimes() async {
        guard let location = locationService.currentLocation else { return }
        
        isLoading = true
        
        do {
            monthlyPrayerTimes = try await prayerTimeService.getMonthlyPrayerTimes(
                for: currentMonth,
                location: location
            )
        } catch {
            print("Failed to load monthly prayer times: \(error)")
        }
        
        isLoading = false
    }
}

// MARK: - Month Navigation View
struct MonthNavigationView: View {
    @Binding var currentMonth: Date
    let onMonthChange: () async -> Void
    
    private var monthFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMMM yyyy"
        return formatter
    }
    
    var body: some View {
        HStack {
            Button(action: previousMonth) {
                Image(systemName: "chevron.left")
                    .font(.title2)
                    .foregroundColor(.accentColor)
            }
            
            Spacer()
            
            Text(monthFormatter.string(from: currentMonth))
                .font(.title2)
                .fontWeight(.semibold)
            
            Spacer()
            
            Button(action: nextMonth) {
                Image(systemName: "chevron.right")
                    .font(.title2)
                    .foregroundColor(.accentColor)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .shadow(color: .black.opacity(0.1), radius: 1, x: 0, y: 1)
    }
    
    private func previousMonth() {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentMonth = Calendar.current.date(byAdding: .month, value: -1, to: currentMonth) ?? currentMonth
        }
        Task {
            await onMonthChange()
        }
    }
    
    private func nextMonth() {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentMonth = Calendar.current.date(byAdding: .month, value: 1, to: currentMonth) ?? currentMonth
        }
        Task {
            await onMonthChange()
        }
    }
}

// MARK: - Calendar Header View
struct CalendarHeaderView: View {
    private let prayerNames = ["Day", "Fajr", "Sunrise", "Dhuhr", "Asr", "Maghrib", "Isha"]
    
    var body: some View {
        HStack {
            ForEach(prayerNames, id: \.self) { name in
                Text(name)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity)
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

// MARK: - Calendar Day Row
struct CalendarDayRow: View {
    let dayNumber: Int
    let prayerTimes: PrayerTimes
    let isToday: Bool
    @EnvironmentObject private var prayerTimeService: PrayerTimeService
    
    var body: some View {
        HStack(spacing: 4) {
            // Day Number
            Text("\(dayNumber)")
                .font(.caption)
                .fontWeight(isToday ? .bold : .medium)
                .foregroundColor(isToday ? .white : .primary)
                .frame(width: 30, height: 30)
                .background(isToday ? Color.accentColor : Color.clear)
                .cornerRadius(15)
            
            // Prayer Times
            ForEach(Prayer.allCases, id: \.self) { prayer in
                let time = prayerTimeService.getPrayerTime(for: prayer, from: prayerTimes)
                let isImportantPrayer = prayer == .fajr || prayer == .maghrib
                
                Text(time, style: .time)
                    .font(.caption2)
                    .fontWeight(isImportantPrayer ? .semibold : .regular)
                    .foregroundColor(isImportantPrayer ? .primary : .secondary)
                    .frame(maxWidth: .infinity)
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(isToday ? Color.accentColor.opacity(0.1) : Color(.systemBackground))
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(isToday ? Color.accentColor : Color.clear, lineWidth: 1)
        )
    }
}

// MARK: - Islamic Calendar Integration
extension CalendarView {
    private var hijriFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.calendar = Calendar(identifier: .islamicUmmAlQura)
        formatter.dateFormat = "dd MMMM yyyy"
        return formatter
    }
    
    private func getHijriDate(for date: Date) -> String {
        return hijriFormatter.string(from: date)
    }
}

// MARK: - Calendar Export Feature
extension CalendarView {
    private func exportCalendar() {
        // Implementation for exporting prayer times to device calendar
        // This would create calendar events for each prayer time
    }
    
    private func shareCalendar() {
        // Implementation for sharing prayer times
        // This could generate a PDF or text format
    }
}

#Preview {
    NavigationStack {
        CalendarView()
            .environmentObject(PrayerTimeService(repository: try! SwiftDataRepository()))
            .environmentObject(LocationService(repository: try! SwiftDataRepository()))
    }
}
