//
//  ChatView.swift
//  PrayerEase
//
//  Created by <PERSON><PERSON><PERSON> on 12/24/24.
//

import SwiftUI

// MARK: - Modern Chat View
struct ChatView: View {
    @EnvironmentObject private var configurationService: ConfigurationService
    @StateObject private var chatController = ChatController()
    @State private var messageText = ""
    @FocusState private var isTextFieldFocused: Bool
    
    var body: some View {
        VStack(spacing: 0) {
            // Messages List
            ScrollViewReader { proxy in
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(chatController.messages) { message in
                            MessageBubble(message: message)
                                .id(message.id)
                        }
                        
                        if chatController.isLoading {
                            TypingIndicator()
                        }
                    }
                    .padding()
                }
                .onChange(of: chatController.messages.count) { _, _ in
                    if let lastMessage = chatController.messages.last {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            proxy.scrollTo(lastMessage.id, anchor: .bottom)
                        }
                    }
                }
            }
            
            // Input Area
            VStack(spacing: 0) {
                Divider()
                
                HStack(spacing: 12) {
                    TextField("Ask about Islamic practices, prayers, or guidance...", text: $messageText, axis: .vertical)
                        .textFieldStyle(.plain)
                        .focused($isTextFieldFocused)
                        .lineLimit(1...4)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(Color(.systemGray6))
                        .cornerRadius(20)
                    
                    Button(action: sendMessage) {
                        Image(systemName: "arrow.up.circle.fill")
                            .font(.title2)
                            .foregroundColor(messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? .gray : .accentColor)
                    }
                    .disabled(messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || chatController.isLoading)
                }
                .padding()
                .background(Color(.systemBackground))
            }
        }
        .navigationTitle("Islamic AI Assistant")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            if chatController.messages.isEmpty {
                addWelcomeMessage()
            }
        }
    }
    
    private func sendMessage() {
        let text = messageText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !text.isEmpty else { return }
        
        messageText = ""
        isTextFieldFocused = false
        
        chatController.sendMessage(text)
    }
    
    private func addWelcomeMessage() {
        let welcomeMessage = Message(
            content: "Assalamu Alaikum! I'm here to help you with Islamic guidance, prayer information, and religious questions. How can I assist you today?",
            isUser: false
        )
        chatController.messages.append(welcomeMessage)
    }
}

// MARK: - Message Bubble
struct MessageBubble: View {
    let message: Message
    
    var body: some View {
        HStack {
            if message.isUser {
                Spacer(minLength: 50)
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(message.content)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(Color.accentColor)
                        .foregroundColor(.white)
                        .cornerRadius(18, corners: [.topLeft, .topRight, .bottomLeft])
                    
                    Text(message.timestamp, style: .time)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            } else {
                VStack(alignment: .leading, spacing: 4) {
                    Text(message.content)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(Color(.systemGray5))
                        .foregroundColor(.primary)
                        .cornerRadius(18, corners: [.topLeft, .topRight, .bottomRight])
                    
                    Text(message.timestamp, style: .time)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                Spacer(minLength: 50)
            }
        }
    }
}

// MARK: - Typing Indicator
struct TypingIndicator: View {
    @State private var animationPhase = 0.0
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                HStack(spacing: 4) {
                    ForEach(0..<3) { index in
                        Circle()
                            .fill(Color.secondary)
                            .frame(width: 8, height: 8)
                            .scaleEffect(1.0 + 0.3 * sin(animationPhase + Double(index) * 0.5))
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color(.systemGray5))
                .cornerRadius(18, corners: [.topLeft, .topRight, .bottomRight])
                
                Text("AI is typing...")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer(minLength: 50)
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 1.0).repeatForever()) {
                animationPhase = .pi * 2
            }
        }
    }
}

// MARK: - Chat Controller
@MainActor
class ChatController: ObservableObject {
    @Published var messages: [Message] = []
    @Published var isLoading = false
    
    private let configurationService = ConfigurationService.shared
    
    func sendMessage(_ content: String) {
        // Add user message
        let userMessage = Message(content: content, isUser: true)
        messages.append(userMessage)
        
        // Simulate AI response (replace with actual OpenAI integration)
        isLoading = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            let responses = [
                "May Allah guide you in your prayers and worship.",
                "Remember to make dua and seek Allah's guidance in all matters.",
                "The five daily prayers are Fajr, Dhuhr, Asr, Maghrib, and Isha.",
                "Alhamdulillahi rabbil alameen. Praise be to Allah, Lord of all the worlds.",
                "May Allah bless you and keep you on the straight path."
            ]
            
            let response = responses.randomElement() ?? "I'm here to help with your Islamic questions."
            let aiMessage = Message(content: response, isUser: false)
            self.messages.append(aiMessage)
            self.isLoading = false
        }
    }
}

// MARK: - Message Model
struct Message: Identifiable, Equatable {
    let id = UUID()
    let content: String
    let isUser: Bool
    let timestamp = Date()
    
    static func == (lhs: Message, rhs: Message) -> Bool {
        lhs.id == rhs.id
    }
}

// MARK: - View Extensions
extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

// Preview temporarily disabled due to SwiftData initialization requirements
