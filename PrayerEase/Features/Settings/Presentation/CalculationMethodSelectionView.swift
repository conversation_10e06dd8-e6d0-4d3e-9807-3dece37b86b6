//
//  CalculationMethodSelectionView.swift
//  PrayerEase
//
//  Created by <PERSON><PERSON><PERSON> on 12/24/24.
//

import SwiftUI
import Adhan

struct CalculationMethodSelectionView: View {
    @EnvironmentObject private var prayerTimeService: PrayerTimeService
    @Environment(\.dismiss) private var dismiss: DismissAction
    
    var body: some View {
        List {
            ForEach(CalculationMethod.allCases, id: \.self) { method in
                Button(action: {
                    selectMethod(method)
                }) {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(method.displayName)
                                .font(.body)
                                .foregroundColor(.primary)
                            
                            Text(methodDescription(for: method))
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        if prayerTimeService.calculationMethod == method {
                            Image(systemName: "checkmark")
                                .foregroundColor(.accentColor)
                        }
                    }
                }
                .buttonStyle(.plain)
            }
        }
        .navigationTitle("Calculation Method")
        .navigationBarTitleDisplayMode(.inline)
    }
    
    private func selectMethod(_ method: CalculationMethod) {
        prayerTimeService.calculationMethod = method
        dismiss()
    }
    
    private func methodDescription(for method: CalculationMethod) -> String {
        switch method {
        case .muslimWorldLeague:
            return "Used in Europe, Far East, parts of US"
        case .egyptian:
            return "Used in Africa, Syria, Iraq, Lebanon, Malaysia, Brunei"
        case .karachi:
            return "Used in Pakistan, Bangladesh, India, Afghanistan"
        case .ummAlQura:
            return "Used in Saudi Arabia"
        case .dubai:
            return "Used in UAE"
        case .moonsightingCommittee:
            return "Used worldwide"
        case .northAmerica:
            return "Used in North America (ISNA)"
        case .kuwait:
            return "Used in Kuwait"
        case .qatar:
            return "Used in Qatar"
        case .singapore:
            return "Used in Singapore"
        case .tehran:
            return "Used in Iran"
        case .turkey:
            return "Used in Turkey"
        case .other:
            return "Custom calculation method"
        }
    }
}

struct MadhabSelectionView: View {
    @EnvironmentObject private var prayerTimeService: PrayerTimeService
    @Environment(\.dismiss) private var dismiss: DismissAction
    
    private let madhabOptions: [Madhab] = [.hanafi, .shafi]
    
    var body: some View {
        List {
            ForEach(madhabOptions, id: \.self) { madhab in
                Button(action: {
                    selectMadhab(madhab)
                }) {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(madhabName(for: madhab))
                                .font(.body)
                                .foregroundColor(.primary)
                            
                            Text(madhabDescription(for: madhab))
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        if prayerTimeService.madhab == madhab {
                            Image(systemName: "checkmark")
                                .foregroundColor(.accentColor)
                        }
                    }
                }
                .buttonStyle(.plain)
            }
        }
        .navigationTitle("Madhab")
        .navigationBarTitleDisplayMode(.inline)
    }
    
    private func selectMadhab(_ madhab: Madhab) {
        prayerTimeService.madhab = madhab
        dismiss()
    }
    
    private func madhabName(for madhab: Madhab) -> String {
        switch madhab {
        case .hanafi:
            return "Hanafi"
        case .shafi:
            return "Shafi, Maliki, Hanbali"
        }
    }
    
    private func madhabDescription(for madhab: Madhab) -> String {
        switch madhab {
        case .hanafi:
            return "Later Asr time calculation"
        case .shafi:
            return "Earlier Asr time calculation (default)"
        }
    }
}

struct PrayerNotificationSettingsView: View {
    @EnvironmentObject private var notificationService: NotificationService
    
    var body: some View {
        List {
            ForEach(Prayer.allCases, id: \.self) { prayer in
                HStack {
                    Image(systemName: prayer.systemImageName)
                        .font(.title3)
                        .foregroundColor(.accentColor)
                        .frame(width: 30)
                    
                    Text(prayer.displayName)
                        .font(.body)
                    
                    Spacer()
                    
                    Toggle("", isOn: binding(for: prayer))
                        .labelsHidden()
                }
                .padding(.vertical, 4)
            }
        }
        .navigationTitle("Prayer Notifications")
        .navigationBarTitleDisplayMode(.inline)
    }
    
    private func binding(for prayer: Prayer) -> Binding<Bool> {
        Binding(
            get: { notificationService.notificationSettings[prayer] ?? false },
            set: { newValue in
                notificationService.updateNotificationSettings(for: prayer, enabled: newValue)
            }
        )
    }
}

struct BeforeNotificationSettingsView: View {
    @EnvironmentObject private var notificationService: NotificationService
    
    var body: some View {
        List {
            Section {
                Picker("Minutes Before", selection: $notificationService.beforeMinutes) {
                    ForEach([5, 10, 15, 20, 30, 45, 60], id: \.self) { minutes in
                        Text("\(minutes) minutes")
                            .tag(minutes)
                    }
                }
                .pickerStyle(.wheel)
            } header: {
                Text("Alert Time")
            } footer: {
                Text("Choose how many minutes before each prayer you'd like to receive a reminder notification.")
            }
            
            Section("Enable Before Alerts") {
                ForEach(Prayer.allCases, id: \.self) { prayer in
                    HStack {
                        Image(systemName: prayer.systemImageName)
                            .font(.title3)
                            .foregroundColor(.accentColor)
                            .frame(width: 30)
                        
                        Text(prayer.displayName)
                            .font(.body)
                        
                        Spacer()
                        
                        Toggle("", isOn: beforeBinding(for: prayer))
                            .labelsHidden()
                    }
                    .padding(.vertical, 4)
                }
            }
        }
        .navigationTitle("Before Prayer Alerts")
        .navigationBarTitleDisplayMode(.inline)
    }
    
    private func beforeBinding(for prayer: Prayer) -> Binding<Bool> {
        Binding(
            get: { notificationService.beforeNotificationSettings[prayer] ?? false },
            set: { newValue in
                notificationService.updateNotificationSettings(for: prayer, enabled: newValue, isBefore: true)
            }
        )
    }
}

// Preview temporarily disabled due to SwiftData initialization requirements
