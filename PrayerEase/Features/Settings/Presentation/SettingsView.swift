//
//  SettingsView.swift
//  PrayerEase
//
//  Created by <PERSON><PERSON><PERSON> on 12/24/24.
//

import SwiftUI
import Adhan

// MARK: - Modern Settings View
struct SettingsView: View {
    @EnvironmentObject private var prayerTimeService: PrayerTimeService
    @EnvironmentObject private var notificationService: NotificationService
    @EnvironmentObject private var configurationService: ConfigurationService
    @EnvironmentObject private var appCoordinator: AppCoordinator
    
    @State private var showingResetAlert = false
    @State private var showingAbout = false
    
    var body: some View {
        Form {
            // Prayer Calculation Settings
            calculationSection
            
            // Notification Settings
            notificationSection
            
            // App Settings
            appSection
            
            // About Section
            aboutSection
        }
        .navigationTitle("Settings")
        .navigationBarTitleDisplayMode(.large)
        .alert("Reset Settings", isPresented: $showingResetAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Reset", role: .destructive) {
                resetSettings()
            }
        } message: {
            Text("This will reset all settings to their default values. This action cannot be undone.")
        }
        .sheet(isPresented: $showingAbout) {
            AboutView()
        }
    }
    
    // MARK: - Calculation Section
    private var calculationSection: some View {
        Section("Prayer Calculation") {
            // Calculation Method
            NavigationLink {
                CalculationMethodSelectionView()
            } label: {
                SettingsRow(
                    title: "Calculation Method",
                    subtitle: prayerTimeService.calculationMethod.displayName,
                    icon: "book.fill",
                    iconColor: .blue
                )
            }
            
            // Madhab
            NavigationLink {
                MadhabSelectionView()
            } label: {
                SettingsRow(
                    title: "Madhab",
                    subtitle: prayerTimeService.madhab == .hanafi ? "Hanafi" : "Shafi, Maliki, Hanbali",
                    icon: "doc.text.fill",
                    iconColor: .green
                )
            }
        }
    }
    
    // MARK: - Notification Section
    private var notificationSection: some View {
        Section("Notifications") {
            // Notification Permission Status
            HStack {
                SettingsRow(
                    title: "Notification Permission",
                    subtitle: notificationService.isAuthorized ? "Enabled" : "Disabled",
                    icon: "bell.fill",
                    iconColor: notificationService.isAuthorized ? .green : .red
                )
                
                if !notificationService.isAuthorized {
                    Button("Enable") {
                        Task {
                            _ = await notificationService.requestNotificationPermission()
                        }
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.small)
                }
            }
            
            // Prayer Notifications
            NavigationLink {
                PrayerNotificationSettingsView()
            } label: {
                SettingsRow(
                    title: "Prayer Notifications",
                    subtitle: "Configure prayer time alerts",
                    icon: "alarm.fill",
                    iconColor: .orange
                )
            }
            
            // Before Prayer Notifications
            NavigationLink {
                BeforeNotificationSettingsView()
            } label: {
                SettingsRow(
                    title: "Before Prayer Alerts",
                    subtitle: "\(notificationService.beforeMinutes) minutes before",
                    icon: "clock.badge.fill",
                    iconColor: .purple
                )
            }
        }
    }
    
    // MARK: - App Section
    private var appSection: some View {
        Section("App Settings") {
            // Location Services
            HStack {
                SettingsRow(
                    title: "Location Services",
                    subtitle: appCoordinator.hasLocationPermission ? "Enabled" : "Disabled",
                    icon: "location.fill",
                    iconColor: appCoordinator.hasLocationPermission ? .green : .red
                )
                
                if !appCoordinator.hasLocationPermission {
                    Button("Enable") {
                        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                            UIApplication.shared.open(settingsUrl)
                        }
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.small)
                }
            }
            
            // Theme (Future feature)
            SettingsRow(
                title: "Theme",
                subtitle: "System",
                icon: "paintbrush.fill",
                iconColor: .indigo
            )
            
            // Language (Future feature)
            SettingsRow(
                title: "Language",
                subtitle: "English",
                icon: "globe",
                iconColor: .cyan
            )
            
            // Reset Settings
            Button(action: { showingResetAlert = true }) {
                SettingsRow(
                    title: "Reset Settings",
                    subtitle: "Restore default settings",
                    icon: "arrow.clockwise",
                    iconColor: .red
                )
            }
            .foregroundColor(.primary)
        }
    }
    
    // MARK: - About Section
    private var aboutSection: some View {
        Section("About") {
            Button(action: { showingAbout = true }) {
                SettingsRow(
                    title: "About PrayerEase",
                    subtitle: "Version \(AppConstants.App.version)",
                    icon: "info.circle.fill",
                    iconColor: .blue
                )
            }
            .foregroundColor(.primary)
            
            // Privacy Policy
            Link(destination: URL(string: "https://prayerease.com/privacy")!) {
                SettingsRow(
                    title: "Privacy Policy",
                    subtitle: "How we protect your data",
                    icon: "hand.raised.fill",
                    iconColor: .green
                )
            }
            
            // Support
            Link(destination: URL(string: "mailto:<EMAIL>")!) {
                SettingsRow(
                    title: "Contact Support",
                    subtitle: "Get help and report issues",
                    icon: "envelope.fill",
                    iconColor: .orange
                )
            }
            
            // Rate App
            Button(action: rateApp) {
                SettingsRow(
                    title: "Rate PrayerEase",
                    subtitle: "Share your feedback",
                    icon: "star.fill",
                    iconColor: .yellow
                )
            }
            .foregroundColor(.primary)
        }
    }
    
    // MARK: - Actions
    private func resetSettings() {
        Task {
            await configurationService.resetToDefaults()
            // Refresh the app coordinator
            await appCoordinator.refreshData()
        }
    }
    
    private func rateApp() {
        if let url = URL(string: "https://apps.apple.com/app/id\(AppConstants.App.bundleIdentifier)") {
            UIApplication.shared.open(url)
        }
    }
}

// MARK: - Settings Row Component
struct SettingsRow: View {
    let title: String
    let subtitle: String
    let icon: String
    let iconColor: Color
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(iconColor)
                .frame(width: 28, height: 28)
                .background(iconColor.opacity(0.1))
                .cornerRadius(6)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.body)
                    .foregroundColor(.primary)
                
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(.vertical, 4)
    }
}

// MARK: - About View
struct AboutView: View {
    @Environment(\.dismiss) private var dismiss: DismissAction
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 24) {
                    // App Icon and Name
                    VStack(spacing: 16) {
                        Image(systemName: "moon.stars.fill")
                            .font(.system(size: 80))
                            .foregroundStyle(
                                LinearGradient(
                                    colors: [.blue, .purple],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                        
                        VStack(spacing: 4) {
                            Text("PrayerEase")
                                .font(.largeTitle)
                                .fontWeight(.bold)
                            
                            Text("Version \(AppConstants.App.version)")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    // Description
                    VStack(alignment: .leading, spacing: 16) {
                        Text("About PrayerEase")
                            .font(.headline)
                        
                        Text("PrayerEase is a modern Islamic prayer times app designed to help Muslims maintain their daily prayers with accurate timing, Qibla direction, and helpful reminders.")
                            .font(.body)
                            .foregroundColor(.secondary)
                        
                        Text("Features:")
                            .font(.headline)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            FeatureRow(icon: "clock.fill", text: "Accurate prayer times based on your location")
                            FeatureRow(icon: "safari.fill", text: "Qibla direction with compass")
                            FeatureRow(icon: "bell.fill", text: "Customizable prayer notifications")
                            FeatureRow(icon: "calendar", text: "Monthly prayer time calendar")
                            FeatureRow(icon: "apple.intelligence", text: "AI-powered Islamic guidance")
                        }
                    }
                    
                    // Credits
                    VStack(spacing: 8) {
                        Text("Made with ❤️ by")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text("Alisultan Abdullah")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        Text("May Allah accept our efforts")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .italic()
                    }
                }
                .padding()
            }
            .navigationTitle("About")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct FeatureRow: View {
    let icon: String
    let text: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.body)
                .foregroundColor(.accentColor)
                .frame(width: 20)
            
            Text(text)
                .font(.body)
                .foregroundColor(.secondary)
            
            Spacer()
        }
    }
}

// Preview temporarily disabled due to SwiftData initialization requirements
