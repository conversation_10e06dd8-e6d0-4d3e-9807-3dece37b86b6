//
//  PrayerEaseApp.swift
//  PrayerEase
//
//  Created by <PERSON><PERSON><PERSON> on 10/30/24.
//

import SwiftUI
import SwiftData
import BackgroundTasks

@main
struct PrayerEaseApp: App {

    @StateObject private var appCoordinator = AppCoordinator()
    @State private var isAppInitialized = false

    var body: some Scene {
        WindowGroup {
            Group {
                if isAppInitialized {
                    ModernTabView()
                        .environmentObject(appCoordinator)
                        .environmentObject(appCoordinator.locationService)
                        .environmentObject(appCoordinator.prayerTimeService)
                        .environmentObject(appCoordinator.notificationService)
                        .environmentObject(appCoordinator.configurationService)
                } else {
                    LoadingView()
                        .environmentObject(appCoordinator)
                }
            }
            .task {
                await initializeApp()
            }
            .onReceive(NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification)) { _ in
                Task {
                    await appCoordinator.handleAppDidBecomeActive()
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: UIApplication.willResignActiveNotification)) { _ in
                appCoordinator.handleAppWillResignActive()
            }
        }
        .modelContainer(appCoordinator.repository.modelContainer)
        .backgroundTask(.appRefresh(AppConstants.BackgroundTasks.notificationRefresh)) {
            await handleBackgroundRefresh()
        }
    }

    // MARK: - App Initialization
    private func initializeApp() async {
        await appCoordinator.initializeApp()

        // Wait for initialization to complete
        while !appCoordinator.isInitialized {
            try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        }

        withAnimation(.easeInOut(duration: 0.5)) {
            isAppInitialized = true
        }
    }

    // MARK: - Background Tasks
    private func handleBackgroundRefresh() async {
        // This will be called when the system performs background app refresh
        await appCoordinator.refreshData()
    }
}

// MARK: - Loading View
struct LoadingView: View {
    @EnvironmentObject private var appCoordinator: AppCoordinator
    @State private var animationPhase = 0.0

    var body: some View {
        VStack(spacing: 30) {
            // App Icon or Logo
            Image(systemName: "moon.stars.fill")
                .font(.system(size: 80))
                .foregroundStyle(
                    LinearGradient(
                        colors: [.blue, .purple],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .scaleEffect(1.0 + sin(animationPhase) * 0.1)
                .animation(
                    .easeInOut(duration: 2.0).repeatForever(autoreverses: true),
                    value: animationPhase
                )

            VStack(spacing: 16) {
                Text("PrayerEase")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Text("Preparing your prayer times...")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                if appCoordinator.isLoading {
                    ProgressView()
                        .scaleEffect(1.2)
                        .tint(.accentColor)
                }
            }

            // Error handling
            if let error = appCoordinator.currentError {
                VStack(spacing: 12) {
                    Text("Setup Error")
                        .font(.headline)
                        .foregroundColor(.red)

                    Text(error.localizedDescription)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)

                    if let suggestion = error.recoverySuggestion {
                        Text(suggestion)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }

                    Button("Retry") {
                        Task {
                            await appCoordinator.retryLastOperation()
                        }
                    }
                    .buttonStyle(.borderedProminent)
                }
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .shadow(radius: 4)
                .padding(.horizontal)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
        .onAppear {
            animationPhase = 1.0
        }
    }
}

#if DEBUG
struct PrayerEaseApp_Previews: PreviewProvider {
    static var previews: some View {
        LoadingView()
            .environmentObject(AppCoordinator())
    }
}
#endif
