//
//  AppConstants.swift
//  PrayerEase
//
//  Created by <PERSON><PERSON><PERSON> on 12/24/24.
//

import Foundation

// MARK: - App Constants
enum AppConstants {
    
    // MARK: - App Information
    enum App {
        static let name = "PrayerEase"
        static let bundleIdentifier = "com.alijaver.PrayerEase"
        static let version = "2.0.0"
        static let appGroup = "group.com.alijaver.PrayerEase"
    }
    
    // MARK: - User Defaults Keys
    enum UserDefaultsKeys {
        static let calculationMethod = "calculation_method"
        static let madhab = "madhab"
        static let notificationSettings = "notification_settings"
        static let beforeNotificationSettings = "before_notification_settings"
        static let beforeMinutes = "before_minutes"
        static let userLocation = "user_location"
        static let locationName = "location_name"
        static let lastLocationUpdate = "last_location_update"
        static let appSettings = "app_settings"
        static let onboardingCompleted = "onboarding_completed"
    }
    
    // MARK: - Background Tasks
    enum BackgroundTasks {
        static let notificationRefresh = "com.alijaver.PrayerEase.refreshNotifications"
        static let prayerTimeUpdate = "com.alijaver.PrayerEase.updatePrayerTimes"
    }
    
    // MARK: - Notification Identifiers
    enum NotificationIdentifiers {
        static let prayerTime = "prayer_time_notification"
        static let beforePrayerTime = "before_prayer_time_notification"
        static let dailyReminder = "daily_reminder_notification"
    }
    
    // MARK: - Location Settings
    enum Location {
        static let desiredAccuracy: Double = 100.0 // meters
        static let distanceFilter: Double = 500.0 // meters
        static let maxLocationAge: TimeInterval = 300 // 5 minutes
        static let qiblaAccuracyThreshold: Double = 5.0 // degrees
    }
    
    // MARK: - Prayer Time Settings
    enum PrayerTime {
        static let maxDaysAhead = 30
        static let cacheExpirationHours = 24
        static let minimumTimeBetweenUpdates: TimeInterval = 3600 // 1 hour
    }
    
    // MARK: - Notification Settings
    enum Notifications {
        static let maxScheduledNotifications = 64 // iOS limit
        static let defaultBeforeMinutes = 15
        static let maxBeforeMinutes = 60
        static let minBeforeMinutes = 1
    }
    
    // MARK: - UI Constants
    enum UI {
        static let animationDuration: Double = 0.3
        static let cornerRadius: Double = 12.0
        static let shadowRadius: Double = 4.0
        static let shadowOpacity: Float = 0.1
        static let minimumTapTargetSize: Double = 44.0
    }
    
    // MARK: - Accessibility
    enum Accessibility {
        static let prayerTimeFormat = "Prayer time for %@ is at %@"
        static let timeRemainingFormat = "%@ remaining until %@"
        static let qiblaDirectionFormat = "Qibla direction is %@ degrees"
        static let notificationToggleFormat = "Toggle notifications for %@"
    }
    
    // MARK: - API Configuration
    enum API {
        static let requestTimeout: TimeInterval = 30.0
        static let maxRetryAttempts = 3
        static let retryDelay: TimeInterval = 1.0
    }
    
    // MARK: - Chat Configuration
    enum Chat {
        static let maxMessageLength = 1000
        static let typingAnimationDuration: Double = 2.0
        static let messageHistoryLimit = 100
    }
    
    // MARK: - Error Messages
    enum ErrorMessages {
        static let locationPermissionDenied = "Location access is required to calculate prayer times. Please enable location services in Settings."
        static let notificationPermissionDenied = "Notification permission is required for prayer time alerts. Please enable notifications in Settings."
        static let networkError = "Unable to connect to the internet. Please check your connection and try again."
        static let calculationError = "Unable to calculate prayer times. Please try again later."
        static let genericError = "An unexpected error occurred. Please try again."
    }
    
    // MARK: - Success Messages
    enum SuccessMessages {
        static let notificationsScheduled = "Prayer time notifications have been scheduled successfully."
        static let settingsSaved = "Settings have been saved successfully."
        static let locationUpdated = "Location has been updated successfully."
    }
    
    // MARK: - Feature Flags
    enum FeatureFlags {
        static let enableChatFeature = true
        static let enableAdvancedNotifications = true
        static let enableLocationCaching = true
        static let enableAnalytics = false
        static let enableDebugMode = false
    }
    
    // MARK: - Widget Configuration
    enum Widget {
        static let refreshInterval: TimeInterval = 900 // 15 minutes
        static let maxEntries = 10
        static let placeholderText = "Prayer times will appear here"
    }
    
    // MARK: - Watch Configuration
    enum Watch {
        static let complicationUpdateInterval: TimeInterval = 300 // 5 minutes
        static let maxComplicationEntries = 5
        static let syncTimeout: TimeInterval = 10.0
    }
}

// MARK: - Environment Configuration
enum AppEnvironment {
    case development
    case staging
    case production
    
    static var current: AppEnvironment {
        #if DEBUG
        return .development
        #elseif STAGING
        return .staging
        #else
        return .production
        #endif
    }
    
    var baseURL: String {
        switch self {
        case .development:
            return "https://dev-api.prayerease.com"
        case .staging:
            return "https://staging-api.prayerease.com"
        case .production:
            return "https://api.prayerease.com"
        }
    }
    
    var enableLogging: Bool {
        switch self {
        case .development, .staging:
            return true
        case .production:
            return false
        }
    }
}
