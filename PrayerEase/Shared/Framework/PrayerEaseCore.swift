//
//  PrayerEaseCore.swift
//  PrayerEase
//
//  Created by <PERSON><PERSON><PERSON> on 12/24/24.
//

import Foundation
import CoreLocation
import Adhan

// MARK: - Shared Core Framework
// This file contains the core business logic that can be shared across iOS, watchOS, and widget extensions

// MARK: - Shared Prayer Time Calculator
public struct SharedPrayerTimeCalculator {
    
    public static func calculatePrayerTimes(
        for date: Date,
        location: CLLocation,
        calculationMethod: CalculationMethod,
        madhab: Madhab
    ) -> PrayerTimes? {
        let coordinates = Coordinates(
            latitude: location.coordinate.latitude,
            longitude: location.coordinate.longitude
        )
        
        var params = calculationMethod.params
        params.madhab = madhab
        
        let dateComponents = Calendar.current.dateComponents([.year, .month, .day], from: date)
        
        return PrayerTimes(
            coordinates: coordinates,
            date: dateComponents,
            calculationParameters: params
        )
    }
    
    public static func getCurrentPrayer(from prayerTimes: PrayerTimes) -> Prayer? {
        return prayerTimes.currentPrayer()
    }
    
    public static func getNextPrayer(from prayerTimes: PrayerTimes) -> Prayer? {
        return prayerTimes.nextPrayer()
    }
    
    public static func getTimeUntilNextPrayer(from prayerTimes: PrayerTimes) -> TimeInterval? {
        guard let nextPrayer = prayerTimes.nextPrayer() else { return nil }
        
        let nextPrayerTime = getPrayerTime(for: nextPrayer, from: prayerTimes)
        return nextPrayerTime.timeIntervalSince(Date())
    }
    
    public static func getPrayerTime(for prayer: Prayer, from prayerTimes: PrayerTimes) -> Date {
        switch prayer {
        case .fajr: return prayerTimes.fajr
        case .sunrise: return prayerTimes.sunrise
        case .dhuhr: return prayerTimes.dhuhr
        case .asr: return prayerTimes.asr
        case .maghrib: return prayerTimes.maghrib
        case .isha: return prayerTimes.isha
        }
    }
}

// MARK: - Shared Qibla Calculator
public struct SharedQiblaCalculator {
    
    public static func calculateQiblaDirection(from location: CLLocation) -> Double {
        let coordinates = Coordinates(
            latitude: location.coordinate.latitude,
            longitude: location.coordinate.longitude
        )
        
        return Qibla(coordinates: coordinates).direction
    }
    
    public static func isPointingToQibla(
        deviceHeading: Double,
        qiblaDirection: Double,
        threshold: Double = 5.0
    ) -> Bool {
        let difference = abs(deviceHeading - qiblaDirection)
        return difference <= threshold
    }
}

// MARK: - Shared Data Models for Cross-Platform Use
public struct SharedPrayerData: Codable {
    public let date: Date
    public let fajr: Date
    public let sunrise: Date
    public let dhuhr: Date
    public let asr: Date
    public let maghrib: Date
    public let isha: Date
    public let calculationMethod: String
    public let madhab: Int
    public let locationName: String
    public let latitude: Double
    public let longitude: Double
    
    public init(
        prayerTimes: PrayerTimes,
        date: Date,
        calculationMethod: CalculationMethod,
        madhab: Madhab,
        locationName: String,
        location: CLLocation
    ) {
        self.date = date
        self.fajr = prayerTimes.fajr
        self.sunrise = prayerTimes.sunrise
        self.dhuhr = prayerTimes.dhuhr
        self.asr = prayerTimes.asr
        self.maghrib = prayerTimes.maghrib
        self.isha = prayerTimes.isha
        self.calculationMethod = calculationMethod.rawValue
        self.madhab = madhab.rawValue
        self.locationName = locationName
        self.latitude = location.coordinate.latitude
        self.longitude = location.coordinate.longitude
    }
    
    public var location: CLLocation {
        CLLocation(latitude: latitude, longitude: longitude)
    }
    
    public var calculationMethodEnum: CalculationMethod {
        CalculationMethod(rawValue: calculationMethod) ?? .turkey
    }
    
    public var madhabEnum: Madhab {
        Madhab(rawValue: madhab) ?? .shafi
    }
}

// MARK: - Shared Settings Model
public struct SharedAppSettings: Codable {
    public let calculationMethod: String
    public let madhab: Int
    public let notificationSettings: [String: Bool]
    public let beforeNotificationSettings: [String: Bool]
    public let beforeMinutes: Int
    
    public init(
        calculationMethod: CalculationMethod,
        madhab: Madhab,
        notificationSettings: [String: Bool],
        beforeNotificationSettings: [String: Bool],
        beforeMinutes: Int
    ) {
        self.calculationMethod = calculationMethod.rawValue
        self.madhab = madhab.rawValue
        self.notificationSettings = notificationSettings
        self.beforeNotificationSettings = beforeNotificationSettings
        self.beforeMinutes = beforeMinutes
    }
    
    public var calculationMethodEnum: CalculationMethod {
        CalculationMethod(rawValue: calculationMethod) ?? .turkey
    }
    
    public var madhabEnum: Madhab {
        Madhab(rawValue: madhab) ?? .shafi
    }
}

// MARK: - Shared Formatters
public struct SharedFormatters {
    
    public static let timeFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter
    }()
    
    public static let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter
    }()
    
    public static let hijriFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.calendar = Calendar(identifier: .islamicUmmAlQura)
        formatter.dateFormat = "dd MMMM yyyy"
        return formatter
    }()
    
    public static func formatTimeRemaining(_ timeInterval: TimeInterval) -> String {
        let hours = Int(timeInterval) / 3600
        let minutes = Int(timeInterval) % 3600 / 60
        
        if hours > 0 {
            return String(format: "%dh %dm", hours, minutes)
        } else {
            return String(format: "%dm", minutes)
        }
    }
    
    public static func formatPrayerTime(_ date: Date) -> String {
        return timeFormatter.string(from: date)
    }
    
    public static func formatDate(_ date: Date) -> String {
        return dateFormatter.string(from: date)
    }
    
    public static func formatHijriDate(_ date: Date) -> String {
        return hijriFormatter.string(from: date)
    }
}

// MARK: - Shared Constants for Cross-Platform Use
public struct SharedConstants {
    
    public struct Location {
        public static let desiredAccuracy: Double = 100.0
        public static let qiblaAccuracyThreshold: Double = 5.0
    }
    
    public struct PrayerTime {
        public static let cacheExpirationHours = 24
        public static let maxDaysAhead = 30
    }
    
    public struct Notifications {
        public static let defaultBeforeMinutes = 15
        public static let maxScheduledNotifications = 64
    }
    
    public struct App {
        public static let name = "PrayerEase"
        public static let version = "2.0.0"
        public static let appGroup = "group.com.alijaver.PrayerEase"
    }
}

// MARK: - Shared User Defaults for App Group
public class SharedUserDefaults {
    
    private static nonisolated(unsafe) let userDefaults = UserDefaults(suiteName: SharedConstants.App.appGroup)
    
    public static func save<T: Codable>(_ object: T, forKey key: String) {
        if let encoded = try? JSONEncoder().encode(object) {
            userDefaults?.set(encoded, forKey: key)
        }
    }
    
    public static func load<T: Codable>(_ type: T.Type, forKey key: String) -> T? {
        guard let data = userDefaults?.data(forKey: key) else { return nil }
        return try? JSONDecoder().decode(type, from: data)
    }
    
    public static func remove(forKey key: String) {
        userDefaults?.removeObject(forKey: key)
    }
    
    public static func savePrayerData(_ prayerData: SharedPrayerData) {
        save(prayerData, forKey: "current_prayer_data")
    }
    
    public static func loadPrayerData() -> SharedPrayerData? {
        return load(SharedPrayerData.self, forKey: "current_prayer_data")
    }
    
    public static func saveAppSettings(_ settings: SharedAppSettings) {
        save(settings, forKey: "app_settings")
    }
    
    public static func loadAppSettings() -> SharedAppSettings? {
        return load(SharedAppSettings.self, forKey: "app_settings")
    }
}

// MARK: - Widget Data Provider
public struct WidgetDataProvider {
    
    public static func getCurrentPrayerData() -> SharedPrayerData? {
        return SharedUserDefaults.loadPrayerData()
    }
    
    public static func getNextPrayerInfo() -> (prayer: Prayer, timeRemaining: TimeInterval)? {
        guard let prayerData = getCurrentPrayerData() else { return nil }
        
        let now = Date()
        let prayers: [(Prayer, Date)] = [
            (.fajr, prayerData.fajr),
            (.sunrise, prayerData.sunrise),
            (.dhuhr, prayerData.dhuhr),
            (.asr, prayerData.asr),
            (.maghrib, prayerData.maghrib),
            (.isha, prayerData.isha)
        ]
        
        for (prayer, time) in prayers {
            if time > now {
                return (prayer, time.timeIntervalSince(now))
            }
        }
        
        // If no prayer today, return tomorrow's Fajr
        if Calendar.current.date(byAdding: .day, value: 1, to: now) != nil {
            // This would need to be calculated or cached
            return (.fajr, 0) // Placeholder
        }
        
        return nil
    }
}

// MARK: - Watch Connectivity Data
public struct WatchConnectivityData: Codable {
    public let prayerData: SharedPrayerData
    public let settings: SharedAppSettings
    public let lastUpdated: Date
    
    public init(prayerData: SharedPrayerData, settings: SharedAppSettings) {
        self.prayerData = prayerData
        self.settings = settings
        self.lastUpdated = Date()
    }
}
