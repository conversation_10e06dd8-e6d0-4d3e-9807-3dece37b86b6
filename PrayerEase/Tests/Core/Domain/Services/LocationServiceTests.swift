//
//  LocationServiceTests.swift
//  PrayerEase
//
//  Created by <PERSON><PERSON><PERSON> on 12/24/24.
//

import Testing
import CoreLocation
@testable import PrayerEase

// MARK: - Location Service Tests
@MainActor
struct LocationServiceTests {
    
    private var mockRepository: MockSwiftDataRepository!
    private var locationService: LocationService!
    
    init() async throws {
        mockRepository = MockSwiftDataRepository()
        locationService = LocationService(repository: mockRepository)
    }
    
    // MARK: - Initialization Tests
    @Test("Location service initializes correctly")
    func testLocationServiceInitialization() async throws {
        #expect(locationService.currentLocation == nil)
        #expect(locationService.locationName == "Unknown Location")
        #expect(locationService.isLocationAuthorized == false)
        #expect(locationService.heading == 0.0)
        #expect(locationService.headingAccuracy == 0.0)
        #expect(locationService.error == nil)
    }
    
    // MARK: - Qibla Calculation Tests
    @Test("Qibla direction calculation for known locations")
    func testQiblaDirectionCalculation() async throws {
        // Test for New York (should point towards Mecca)
        let newYorkLocation = CLLocation(latitude: 40.7128, longitude: -74.0060)
        
        // Manually set location for testing
        await locationService.handleLocationUpdate(newYorkLocation)
        
        let qiblaDirection = locationService.calculateQiblaDirection()
        
        #expect(qiblaDirection != nil)
        #expect(qiblaDirection! > 0)
        #expect(qiblaDirection! < 360)
        
        // For New York, Qibla direction should be approximately 58-60 degrees
        #expect(abs(qiblaDirection! - 59) < 5, "Qibla direction for New York should be around 59 degrees")
    }
    
    @Test("Qibla direction calculation for Mecca")
    func testQiblaDirectionForMecca() async throws {
        // Test for Mecca itself (Qibla direction should be 0 or undefined)
        let meccaLocation = CLLocation(latitude: 21.4225, longitude: 39.8262)
        
        await locationService.handleLocationUpdate(meccaLocation)
        
        let qiblaDirection = locationService.calculateQiblaDirection()
        
        #expect(qiblaDirection != nil)
        // In Mecca, any direction could be considered valid, but the calculation should still work
    }
    
    // MARK: - Location Validation Tests
    @Test("Location accuracy validation")
    func testLocationAccuracyValidation() async throws {
        // Test with high accuracy location
        let accurateLocation = CLLocation(
            coordinate: CLLocationCoordinate2D(latitude: 40.7128, longitude: -74.0060),
            altitude: 10,
            horizontalAccuracy: 5, // Very accurate
            verticalAccuracy: 5,
            timestamp: Date()
        )
        
        await locationService.handleLocationUpdate(accurateLocation)
        #expect(locationService.currentLocation != nil)
        
        // Test with low accuracy location (should be rejected)
        let inaccurateLocation = CLLocation(
            coordinate: CLLocationCoordinate2D(latitude: 40.7128, longitude: -74.0060),
            altitude: 10,
            horizontalAccuracy: 1000, // Very inaccurate
            verticalAccuracy: 1000,
            timestamp: Date()
        )
        
        await locationService.handleLocationUpdate(inaccurateLocation)
        // Should still have the previous accurate location
        #expect(locationService.currentLocation?.horizontalAccuracy == 5)
    }
    
    // MARK: - Error Handling Tests
    @Test("Location error handling")
    func testLocationErrorHandling() async throws {
        let testError = CLError(.denied)
        
        await locationService.handleLocationError(testError)
        
        #expect(locationService.error != nil)
        #expect(locationService.error == .permissionDenied)
    }
    
    // MARK: - Cache Tests
    @Test("Location data caching")
    func testLocationDataCaching() async throws {
        let testLocation = CLLocation(latitude: 40.7128, longitude: -74.0060)
        
        await locationService.handleLocationUpdate(testLocation)
        
        // Verify location was saved to repository
        let savedLocations = try mockRepository.fetchActiveLocationData()
        #expect(savedLocations.count == 1)
        #expect(savedLocations.first?.latitude == 40.7128)
        #expect(savedLocations.first?.longitude == -74.0060)
    }
}

// MARK: - Mock Repository
class MockSwiftDataRepository: SwiftDataRepository {
    private var locationData: [LocationData] = []
    private var userSettings: UserSettings?

    override init() throws {
        // Initialize without actual SwiftData for testing
        // Skip the parent initializer that requires SwiftData
    }
    
    override func saveLocationData(_ locationData: LocationData) throws {
        // Deactivate previous locations
        for location in self.locationData {
            location.isActive = false
        }
        
        self.locationData.append(locationData)
    }
    
    override func fetchActiveLocationData() throws -> [LocationData] {
        return locationData.filter { $0.isActive }
    }
    
    override func fetchLocationData(limit: Int = 10) throws -> [LocationData] {
        return Array(locationData.prefix(limit))
    }
    
    override func saveUserSettings(_ settings: UserSettings) throws {
        self.userSettings = settings
    }
    
    override func fetchUserSettings() throws -> UserSettings? {
        return userSettings
    }
    
    override func updateUserSettings(_ settings: UserSettings) throws {
        settings.updateTimestamp()
    }
    
    override func deleteAll() throws {
        locationData.removeAll()
        userSettings = nil
    }
}

// MARK: - Test Extensions
extension LocationService {
    func handleLocationUpdate(_ location: CLLocation) async {
        await self.handleNewLocation(location)
    }
    
    func handleLocationError(_ error: Error) async {
        await self.handleLocationError(error)
    }
}

// MARK: - Location Error Equatable
extension LocationError: Equatable {
    public static func == (lhs: LocationError, rhs: LocationError) -> Bool {
        switch (lhs, rhs) {
        case (.permissionDenied, .permissionDenied),
             (.locationUnavailable, .locationUnavailable),
             (.networkError, .networkError):
            return true
        case (.unknown(let lhsError), .unknown(let rhsError)):
            return lhsError.localizedDescription == rhsError.localizedDescription
        default:
            return false
        }
    }
}
