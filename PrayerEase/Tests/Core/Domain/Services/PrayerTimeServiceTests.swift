//
//  PrayerTimeServiceTests.swift
//  PrayerEase
//
//  Created by <PERSON><PERSON><PERSON> on 12/24/24.
//

import Testing
import CoreLocation
import <PERSON>han
@testable import PrayerEase

// MARK: - Prayer Time Service Tests
@MainActor
struct PrayerTimeServiceTests {
    
    private var mockRepository: MockSwiftDataRepository!
    private var prayerTimeService: PrayerTimeService!
    
    init() async throws {
        mockRepository = MockSwiftDataRepository()
        prayerTimeService = PrayerTimeService(repository: mockRepository)
    }
    
    // MARK: - Initialization Tests
    @Test("Prayer time service initializes correctly")
    func testPrayerTimeServiceInitialization() async throws {
        #expect(prayerTimeService.currentPrayerTimes == nil)
        #expect(prayerTimeService.calculationMethod == .turkey) // Default
        #expect(prayerTimeService.madhab == .shafi) // Default
        #expect(prayerTimeService.nextPrayer == nil)
        #expect(prayerTimeService.timeUntilNextPrayer == nil)
    }
    
    // MARK: - Prayer Time Calculation Tests
    @Test("Prayer time calculation for known location")
    func testPrayerTimeCalculation() async throws {
        let newYorkLocation = CLLocation(latitude: 40.7128, longitude: -74.0060)
        let testDate = Date()
        
        let prayerTimes = try await prayerTimeService.calculatePrayerTimes(
            for: testDate,
            location: newYorkLocation
        )
        
        #expect(prayerTimes.fajr < prayerTimes.sunrise)
        #expect(prayerTimes.sunrise < prayerTimes.dhuhr)
        #expect(prayerTimes.dhuhr < prayerTimes.asr)
        #expect(prayerTimes.asr < prayerTimes.maghrib)
        #expect(prayerTimes.maghrib < prayerTimes.isha)
        
        // Verify times are within reasonable bounds (not in the past by more than a day)
        let oneDayAgo = Calendar.current.date(byAdding: .day, value: -1, to: testDate)!
        #expect(prayerTimes.fajr > oneDayAgo)
        #expect(prayerTimes.isha > oneDayAgo)
    }
    
    @Test("Prayer time calculation with different methods")
    func testDifferentCalculationMethods() async throws {
        let location = CLLocation(latitude: 40.7128, longitude: -74.0060)
        let testDate = Date()
        
        // Test with Muslim World League
        prayerTimeService.calculationMethod = .muslimWorldLeague
        let mwlTimes = try await prayerTimeService.calculatePrayerTimes(
            for: testDate,
            location: location
        )
        
        // Test with ISNA
        prayerTimeService.calculationMethod = .northAmerica
        let isnaTimes = try await prayerTimeService.calculatePrayerTimes(
            for: testDate,
            location: location
        )
        
        // Different methods should produce different times (at least for some prayers)
        let timesAreDifferent = mwlTimes.fajr != isnaTimes.fajr ||
                               mwlTimes.isha != isnaTimes.isha
        
        #expect(timesAreDifferent, "Different calculation methods should produce different prayer times")
    }
    
    @Test("Prayer time calculation with different madhabs")
    func testDifferentMadhabs() async throws {
        let location = CLLocation(latitude: 40.7128, longitude: -74.0060)
        let testDate = Date()
        
        // Test with Shafi madhab
        prayerTimeService.madhab = .shafi
        let shafiTimes = try await prayerTimeService.calculatePrayerTimes(
            for: testDate,
            location: location
        )
        
        // Test with Hanafi madhab
        prayerTimeService.madhab = .hanafi
        let hanafiTimes = try await prayerTimeService.calculatePrayerTimes(
            for: testDate,
            location: location
        )
        
        // Hanafi Asr should be later than Shafi Asr
        #expect(hanafiTimes.asr > shafiTimes.asr, "Hanafi Asr time should be later than Shafi Asr time")
    }
    
    // MARK: - Monthly Prayer Times Tests
    @Test("Monthly prayer times calculation")
    func testMonthlyPrayerTimes() async throws {
        let location = CLLocation(latitude: 40.7128, longitude: -74.0060)
        let testDate = Date()
        
        let monthlyTimes = try await prayerTimeService.getMonthlyPrayerTimes(
            for: testDate,
            location: location
        )
        
        let daysInMonth = Calendar.current.range(of: .day, in: .month, for: testDate)?.count ?? 30
        #expect(monthlyTimes.count == daysInMonth)
        
        // Verify times are in chronological order for each day
        for prayerTimes in monthlyTimes {
            #expect(prayerTimes.fajr < prayerTimes.sunrise)
            #expect(prayerTimes.sunrise < prayerTimes.dhuhr)
            #expect(prayerTimes.dhuhr < prayerTimes.asr)
            #expect(prayerTimes.asr < prayerTimes.maghrib)
            #expect(prayerTimes.maghrib < prayerTimes.isha)
        }
    }
    
    // MARK: - Current Prayer Detection Tests
    @Test("Current prayer detection")
    func testCurrentPrayerDetection() async throws {
        let location = CLLocation(latitude: 40.7128, longitude: -74.0060)
        let testDate = Date()
        
        let prayerTimes = try await prayerTimeService.calculatePrayerTimes(
            for: testDate,
            location: location
        )
        
        // Set current prayer times to enable current prayer detection
        prayerTimeService.currentPrayerTimes = prayerTimes
        
        let currentPrayer = prayerTimeService.getCurrentPrayer()
        let nextPrayer = prayerTimeService.getNextPrayer()
        
        // Should have either a current prayer or a next prayer
        #expect(currentPrayer != nil || nextPrayer != nil)
        
        // If there's both current and next prayer, next should come after current
        if let current = currentPrayer, let next = nextPrayer {
            let currentTime = prayerTimeService.getPrayerTime(for: current, from: prayerTimes)
            let nextTime = prayerTimeService.getPrayerTime(for: next, from: prayerTimes)
            
            // This might not always be true if we're at the end of the day
            // but it's a reasonable check for most cases
            if Calendar.current.isDate(currentTime, inSameDayAs: nextTime) {
                #expect(nextTime > currentTime)
            }
        }
    }
    
    // MARK: - Time Formatting Tests
    @Test("Prayer time formatting")
    func testPrayerTimeFormatting() async throws {
        let testDate = Date()
        let formattedTime = prayerTimeService.formatPrayerTime(testDate)
        
        #expect(!formattedTime.isEmpty)
        #expect(formattedTime.contains(":")) // Should contain time separator
    }
    
    @Test("Time remaining calculation")
    func testTimeRemainingCalculation() async throws {
        // Set a mock time until next prayer (30 minutes)
        prayerTimeService.timeUntilNextPrayer = 30 * 60 // 30 minutes in seconds
        
        let timeRemaining = prayerTimeService.getTimeRemaining(until: .fajr)
        
        #expect(timeRemaining != nil)
        #expect(timeRemaining!.contains("30m"))
    }
    
    // MARK: - Settings Persistence Tests
    @Test("Settings persistence")
    func testSettingsPersistence() async throws {
        // Change settings
        prayerTimeService.calculationMethod = .muslimWorldLeague
        prayerTimeService.madhab = .hanafi
        
        // Wait for settings to be saved
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        // Verify settings were saved
        let savedSettings = try mockRepository.fetchUserSettings()
        #expect(savedSettings != nil)
        #expect(savedSettings?.calculationMethod == .muslimWorldLeague)
        #expect(savedSettings?.madhab == .hanafi)
    }
    
    // MARK: - Cache Management Tests
    @Test("Prayer time cache management")
    func testCacheManagement() async throws {
        let location = CLLocation(latitude: 40.7128, longitude: -74.0060)
        let testDate = Date()
        
        // Calculate prayer times (should cache them)
        let firstCalculation = try await prayerTimeService.calculatePrayerTimes(
            for: testDate,
            location: location
        )
        
        // Calculate again (should use cache)
        let secondCalculation = try await prayerTimeService.calculatePrayerTimes(
            for: testDate,
            location: location
        )
        
        // Times should be identical (from cache)
        #expect(firstCalculation.fajr == secondCalculation.fajr)
        #expect(firstCalculation.isha == secondCalculation.isha)
        
        // Clear cache
        prayerTimeService.clearCache()
        
        // Calculate again (should recalculate)
        let thirdCalculation = try await prayerTimeService.calculatePrayerTimes(
            for: testDate,
            location: location
        )
        
        // Should still be the same times (same input parameters)
        #expect(firstCalculation.fajr == thirdCalculation.fajr)
    }
    
    // MARK: - Error Handling Tests
    @Test("Invalid location handling")
    func testInvalidLocationHandling() async throws {
        // Test with invalid coordinates
        let invalidLocation = CLLocation(latitude: 200, longitude: 200) // Invalid coordinates
        let testDate = Date()
        
        do {
            _ = try await prayerTimeService.calculatePrayerTimes(
                for: testDate,
                location: invalidLocation
            )
            #expect(Bool(false), "Should have thrown an error for invalid location")
        } catch {
            // Expected to throw an error
            #expect(error is PrayerTimeError)
        }
    }
}
