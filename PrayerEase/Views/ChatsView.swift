//
//  SwiftUIView.swift
//  PrayerEase
//
//  Created by <PERSON><PERSON><PERSON> on 12/22/24.
//

import SwiftUI

struct ChatsView: View {
    var body: some View {
        ZStack {
            Color.white
                .edgesIgnoringSafeArea(.all)

            VStack(spacing: 0) {
                NavigationBar()
                Divider()
                    .background(Color.black.opacity(0.3))
                ChatList()
                Spacer()
                TabBar()
            }
        }
    }
}

struct NavigationBar: View {
    var body: some View {
        ZStack {
            Rectangle()
                .fill(.ultraThinMaterial)  // Replace UIBlurEffect
                .frame(height: 88)
                .shadow(color: Color.black.opacity(0.3), radius: 0, y: 0.5)

            Text("Chats")
                .font(.system(size: 17, weight: .semibold))
                .foregroundColor(.black)
                .frame(maxWidth: .infinity, alignment: .center)
                .padding(.top, 44)
        }
    }
}

struct ChatList: View {
    var body: some View {
        VStack(spacing: 0) {
            Chat<PERSON>ow(
                name: "<PERSON>", message: "Thank you! That was very helpful!",
                imageHash: "34134555ee1d5e8149ec891d092dbb246ae04aec")
            Divider().background(Color.black.opacity(0.3))
            ChatRow(
                name: "<PERSON>", message: "I know... I’m trying to get the funds.",
                imageHash: "41333e90c4e76138cf8b06b4700109d112563b79")
            Divider().background(Color.black.opacity(0.3))
            ChatRow(
                name: "Beth Williams",
                message:
                    "I’m looking for tips around capturing the milky way. I have a 6D with a 24-100mm...",
                imageHash: "6f4ae089b252688e26d038f2271ab651705640e9")
            Divider().background(Color.black.opacity(0.3))
            ChatRow(
                name: "Rev Shawn",
                message: "Wanted to ask if you’re available for a portrait shoot next week.",
                imageHash: "9494ed1b997f19ca25e99aaee58cbaa7ed86d004")
            Divider().background(Color.black.opacity(0.3))
        }
    }
}

struct ChatRow: View {
    let name: String
    let message: String
    let imageHash: String

    var body: some View {
        HStack {
            Image(uiImage: UIImage(named: imageHash) ?? UIImage())
                .resizable()
                .frame(width: 64, height: 64)
                .background(Color.gray)
                .clipShape(Circle())
                .padding(.leading, 16)

            VStack(alignment: .leading, spacing: 4) {
                Text(name)
                    .font(.system(size: 13, weight: .bold))
                    .foregroundColor(.black)
                Text(message)
                    .font(.system(size: 13))
                    .foregroundColor(.black)
            }
            .padding(.leading, 16)

            Spacer()
        }
        .frame(height: 64)
    }
}

struct TabBar: View {
    var body: some View {
        ZStack {
            Rectangle()
                .fill(Color.white)
                .frame(height: 83)
                .background(BlurView(style: .systemMaterial))
                .shadow(color: Color.black.opacity(0.3), radius: 0, x: 0, y: -0.5)

            HStack {
                Spacer()
                Image(systemName: "house.fill")
                    .resizable()
                    .frame(width: 24, height: 24)
                    .foregroundColor(.black.opacity(0.8))
                Spacer()
                Image(systemName: "magnifyingglass")
                    .resizable()
                    .frame(width: 24, height: 24)
                    .foregroundColor(.black.opacity(0.8))
                Spacer()
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [Color.red, Color.orange]),
                                startPoint: .topLeading, endPoint: .bottomTrailing)
                        )
                        .frame(width: 40, height: 40)
                    Image(systemName: "plus")
                        .resizable()
                        .frame(width: 13, height: 13)
                        .foregroundColor(.white)
                }
                Spacer()
                Image(systemName: "message.fill")
                    .resizable()
                    .frame(width: 24, height: 24)
                    .foregroundColor(.black.opacity(0.8))
                Spacer()
                Image(systemName: "bell.fill")
                    .resizable()
                    .frame(width: 24, height: 24)
                    .foregroundColor(.black.opacity(0.8))
                Spacer()
            }
            .padding(.horizontal, 16)
        }
    }
}

struct BlurView: UIViewRepresentable {
    var style: UIBlurEffect.Style

    func makeUIView(context: Context) -> UIVisualEffectView {
        return UIVisualEffectView(effect: UIBlurEffect(style: style))
    }

    func updateUIView(_ uiView: UIVisualEffectView, context: Context) {}
}

#Preview {
    ChatsView()
}
