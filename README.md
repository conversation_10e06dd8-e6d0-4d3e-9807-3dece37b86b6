# PrayerEase - Modern Islamic Prayer Times App

A comprehensive, modern SwiftUI application for Islamic prayer times, built with Swift 6+, SwiftData, and following Apple's latest design patterns and best practices.

## 🌟 Features

### Core Features
- **Accurate Prayer Times**: Calculate prayer times based on user location using multiple Islamic calculation methods
- **Qibla Direction**: Real-time compass showing direction to Mecca with haptic feedback
- **Smart Notifications**: Background prayer time notifications that work even when the app is closed
- **Monthly Calendar**: View prayer times for the entire month with Islamic calendar integration
- **AI Assistant**: Islamic guidance and prayer information using OpenAI integration

### Technical Features
- **Swift 6+ with Strict Concurrency**: Modern async/await patterns throughout
- **SwiftData Persistence**: Modern data layer replacing Core Data
- **Protocol-Oriented Architecture**: Clean, testable, and maintainable code structure
- **Comprehensive Testing**: Unit and integration tests using SwiftTesting framework
- **Accessibility Ready**: Full VoiceOver and accessibility support
- **Future-Ready**: Structured for Apple Watch, widgets, and shared frameworks

## 🏗️ Architecture

### Modern Architecture Patterns
- **MVVM + Coordinator Pattern**: Clear separation of concerns
- **Protocol-Oriented Design**: Maximum code reuse and testability
- **Dependency Injection**: Loose coupling and easy testing
- **Repository Pattern**: Abstracted data layer
- **Service Layer**: Business logic separation

### Project Structure
```
PrayerEase/
├── Core/
│   ├── Data/
│   │   ├── Models/           # SwiftData models
│   │   └── Repositories/     # Data access layer
│   ├── Domain/
│   │   ├── Protocols/        # Service protocols
│   │   └── Services/         # Business logic services
│   └── Presentation/
│       ├── AppCoordinator.swift
│       └── ModernTabView.swift
├── Features/
│   ├── PrayerTimes/
│   ├── Qibla/
│   ├── Chat/
│   ├── Calendar/
│   └── Settings/
├── Shared/
│   ├── Constants/
│   ├── Extensions/
│   ├── Utils/
│   └── Framework/           # Cross-platform shared code
└── Tests/
    └── Core/
        └── Domain/
            └── Services/
```

## 🚀 Getting Started

### Prerequisites
- Xcode 16.0+
- iOS 18.0+
- Swift 6.0+

### Installation
1. Clone the repository
```bash
git clone https://github.com/yourusername/PrayerEase.git
cd PrayerEase
```

2. Open the project in Xcode
```bash
open PrayerEase.xcodeproj
```

3. Configure API Keys (Optional - for AI chat feature)
   - Create `APIKeys.plist` in the project root
   - Add your OpenAI API key:
   ```xml
   <?xml version="1.0" encoding="UTF-8"?>
   <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
   <plist version="1.0">
   <dict>
       <key>OPENAI_API_KEY</key>
       <string>your_openai_api_key_here</string>
   </dict>
   </plist>
   ```

4. Build and run the project

## 🔧 Configuration

### Prayer Calculation Methods
The app supports multiple Islamic calculation methods:
- Muslim World League
- Egyptian General Authority
- University of Islamic Sciences, Karachi
- Umm Al-Qura University, Makkah
- Dubai
- Moonsighting Committee Worldwide
- Islamic Society of North America (ISNA)
- Kuwait, Qatar, Singapore, Tehran, Turkey

### Madhab Support
- **Hanafi**: Later Asr time calculation
- **Shafi/Maliki/Hanbali**: Earlier Asr time calculation (default)

### Notification Features
- **Prayer Time Alerts**: Notifications at exact prayer times
- **Before Prayer Alerts**: Customizable reminders (5-60 minutes before)
- **Background Notifications**: Work even when app is closed
- **Smart Scheduling**: Automatically schedules notifications for upcoming days

## 🧪 Testing

The project includes comprehensive tests using SwiftTesting framework:

```bash
# Run all tests
⌘ + U in Xcode

# Run specific test suite
# Use Xcode's test navigator or command line
```

### Test Coverage
- **Location Services**: GPS accuracy, Qibla calculations, error handling
- **Prayer Time Calculations**: Multiple methods, madhabs, edge cases
- **Notification System**: Scheduling, permissions, background tasks
- **Data Persistence**: SwiftData operations, caching, migrations

## 📱 Future Compatibility

The codebase is structured to easily support:

### Apple Watch App
- Shared business logic in `PrayerEaseCore.swift`
- Cross-platform data models
- Watch-optimized UI components ready to implement

### Widget Extensions
- `WidgetDataProvider` for data sharing
- App Group configuration for data synchronization
- Timeline management for prayer time updates

### Shared Frameworks
- Core business logic extracted to reusable components
- Protocol-based architecture for easy extension
- Shared constants and utilities

## 🔐 Privacy & Security

- **Location Privacy**: Clear usage descriptions and minimal data collection
- **API Key Security**: Secure storage recommendations and environment-based configuration
- **Data Protection**: Local-only storage with optional cloud sync
- **Notification Privacy**: User-controlled notification settings

## 🌍 Localization

Ready for internationalization with:
- Localized prayer method names
- RTL language support preparation
- Islamic calendar integration
- Cultural prayer time preferences

## 📋 Requirements

### System Requirements
- iOS 18.0+
- Xcode 16.0+
- Swift 6.0+

### Permissions Required
- **Location Services**: For accurate prayer time calculations
- **Notifications**: For prayer time alerts
- **Background App Refresh**: For notification scheduling

### Dependencies
- **Adhan**: Islamic prayer time calculations
- **OpenAI**: AI chat functionality (optional)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow Swift 6 concurrency patterns
- Write comprehensive tests for new features
- Maintain protocol-oriented design principles
- Update documentation for API changes
- Ensure accessibility compliance

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Batoul Apps**: For the excellent Adhan Swift library
- **Islamic Society of North America**: For calculation method standards
- **Muslim World League**: For global Islamic calendar standards
- **OpenAI**: For AI-powered Islamic guidance features

## 📞 Support

- **Email**: <EMAIL>
- **Issues**: [GitHub Issues](https://github.com/yourusername/PrayerEase/issues)
- **Documentation**: [Wiki](https://github.com/yourusername/PrayerEase/wiki)

---

**Made with ❤️ for the Muslim community**

*May Allah accept our efforts and make this app beneficial for Muslims worldwide.*
